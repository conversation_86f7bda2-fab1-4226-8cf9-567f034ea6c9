# 微信消息发送流程分析

## 消息对象构造

消息对象通过 `qk.t7` 类进行构造，主要设置以下属性：

- `J1(wxid)`: 设置接收人ID，例如 `wxid_uisi5rto449h22`
- `e1(timestamp)`: 设置消息时间戳，例如 `1752571565571`
- `n1(flag)`: 设置消息标识，通常为 `1`
- `d1(content)`: 设置消息内容，例如 `Qwwwq`
- `setType(type)`: 设置消息类型，`1` 表示文本消息

   sendMessage('wxid_uisi5rto449h22', 'Qwwwq', 1);
消息构造完成后，可以通过 `getContent()` 方法获取内容，`getCreateTime()` 获取创建时间。

## 核心发送方法

`et0.o.i` 是微信消息发送的核心入口方法：

```
[+] et0.o.i called
  z: vp4.z@b7a87d7
  b: et0.e@42c3acd
  cont: [object Object]
  z.getClass(): class vp4.z
```

该方法接收三个参数：
- 第一个参数 `z`：类型为 `vp4.z`，可能是消息容器
- 第二个参数 `b`：类型为 `et0.e`，可能是消息配置
- 第三个参数 `cont`：可能是回调对象

## 调用链路

消息发送的完整调用堆栈：

```
et0.o.i(Native Method)
→ vp4.c0.invokeSuspend(Unknown Source:46)
→ dd5.a.resumeWith(Unknown Source:8)
→ kotlinx.coroutines.m1.run(Unknown Source:126)
→ a95.e.run(Unknown Source:63)
→ java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:463)
→ java.util.concurrent.FutureTask.run(FutureTask.java:264)
→ m95.l.run(Unknown Source:242)
→ java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
→ java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
→ f95.c.run(Unknown Source:2)
→ java.lang.Thread.run(Thread.java:1012)
```

## Pipeline处理架构

微信采用Pipeline流水线架构处理消息发送：

### 主要Pipeline组件

1. **Pipeline_SendMsgMgr_Main**：主处理管道
   - 实现类: `np4.q`, `vp4.o`, `yp4.h`
   - 负责消息的主要处理流程

2. **Pipeline_SendMsgMgr_Processor**：处理器管道
   - 实现类: `np4.q`, `vp4.c0`
   - 负责具体的消息处理逻辑

3. **SendMsgService**：发送服务
   - 负责实际的消息发送操作
   - 使用 `Worker` 线程处理

## 协程处理机制

微信使用Kotlin协程处理异步消息发送：

1. `kotlinx.coroutines.internal.DispatchedContinuation`：协程调度
2. `kotlinx.coroutines.r`：协程运行时
3. `TPCR-CancellableContinuation`：可取消的协程

协程允许微信在不阻塞UI线程的情况下处理消息发送，提高应用响应性。

## UI交互组件

消息发送涉及的UI组件：

1. **输入组件**：
   - `com.tencent.mm.ui.widget.MMEditText`：基础输入框
   - `com.tencent.mm.ui.widget.cedit.api.MMFlexEditText`：增强输入框

2. **聊天界面组件**：
   - `com.tencent.mm.pluginsdk.ui.chat.ChatFooter`：聊天底部栏
   - `com.tencent.mm.pluginsdk.ui.chat.v0`：可能是发送按钮

3. **点击处理**：
   - `android.view.View.performClick`
   - `android.view.View.performClickInternal`
   - `android.view.View$PerformClick.run`

## 实际消息发送流程分析

根据Hook日志分析，完整的消息发送流程如下：

### 1. 用户界面交互阶段

首先是用户点击发送按钮，触发UI事件：

```
[+] 发送按钮点击: v0.onClick
    View: android.widget.Button{660d0c4 VFED..C.. ...P.... 22,11-184,97 #7f090f05 app:id/bql}
[+] 聊天底部栏操作: ChatFooter.A
```

这表明用户点击了ID为`app:id/bql`的发送按钮，然后触发了`ChatFooter.A`方法。

### 2. 消息构造阶段

接下来，系统构造消息对象，设置必要的属性：

```
[+] 设置消息接收人: wxid_uisi5rto449h22
[+] 设置消息内容: 123456789
[+] 设置消息类型: 1
[+] 获取消息内容: 123456789
```

这里可以看到消息的接收人ID、内容和类型（1表示文本消息）。

### 3. Pipeline处理阶段

消息通过多个Pipeline组件进行处理：

```
[+] Pipeline处理: np4.q.invokeSuspend
    参数: true
[+] Pipeline处理: np4.q.invokeSuspend
    参数: kotlin.Unit
[+] Pipeline处理: vp4.c0.invokeSuspend
    参数: kotlin.Unit
```

可以观察到消息经过了多次`np4.q.invokeSuspend`和`vp4.c0.invokeSuspend`处理，这些是协程处理的关键环节。参数`kotlin.Unit`相当于Java中的`void`返回类型，表示操作完成。

### 4. 核心发送方法调用

消息处理过程中，调用核心发送方法：

```
[+] 调用发送方法 et0.o.i
    参数0: vp4.z@2f21a56 (vp4.z)
    参数1: et0.e@ac56191 (et0.e)
    参数2: [object Object]
    返回结果: et0.f@ad90d73
```

这里可以看到`et0.o.i`方法接收了三个参数，并返回了一个`et0.f`类型的对象，可能代表发送结果。

### 5. 消息确认和后处理

发送后，系统多次获取消息内容，可能用于确认、日志记录或UI更新：

```
[+] 获取消息内容: 123456789
[+] 获取消息内容: 123456789
[+] 获取消息内容: 123456789
```

### 6. 异常处理

在某些情况下，可能会出现任务取消的情况：

```
[+] Pipeline处理: np4.q.invokeSuspend
    参数: Failure(java.util.concurrent.CancellationException: The task was rejected)
```

这表明某些消息处理任务可能被取消或拒绝。

## 消息发送的完整流程图

```
用户点击发送按钮(v0.onClick)
       ↓
聊天底部栏处理(ChatFooter.A)
       ↓
构造消息对象(qk.t7)
  ├── 设置接收人(J1)
  ├── 设置内容(d1)
  └── 设置类型(setType)
       ↓
Pipeline处理开始(np4.q.invokeSuspend)
       ↓
调用核心发送方法(et0.o.i)
       ↓
消息处理器处理(vp4.c0.invokeSuspend)
       ↓
多次Pipeline处理和确认
       ↓
消息发送完成
```

## 关键类和方法

| 类/方法 | 功能描述 |
|---------|---------|
| `qk.t7` | 消息对象类 |
| `et0.o.i` | 消息发送核心方法 |
| `vp4.z` | 消息容器类 |
| `et0.e` | 消息配置类 |
| `np4.q` | Pipeline处理组件 |
| `vp4.c0` | Pipeline处理组件 |
| `ChatFooter.A` | 聊天底部栏处理方法 |

## Hook建议

基于分析，以下是可能的Hook点：

1. **消息构造Hook**：
   - 目标：`qk.t7` 的 `d1`, `J1` 等方法
   - 可修改消息内容、接收者等

2. **发送流程Hook**：
   - 目标：`et0.o.i` 方法
   - 可拦截所有发送的消息

3. **UI交互Hook**：
   - 目标：`ChatFooter.A` 和 `v0.onClick`
   - 可在UI交互层面拦截发送操作

4. **Pipeline处理Hook**：
   - 目标：`np4.q`, `vp4.c0` 等类的方法
   - 可在消息处理流程中进行干预

## 消息内容分析

从日志中可以看到多种消息内容被处理：

- 数字消息: "123456789", "123456"
- 短文本消息: "Qq", "Qqqq", "Eww", "Qqq", "Rre"
- 中文消息: "衡山路"
- 特殊格式: "@11"
- 测试消息: "Hello from Frida v2!"

这些消息内容的获取调用表明系统在发送过程中和历史记录显示时都会调用`getContent()`方法。 