#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备屏幕显示和控制组件
"""

import sys
import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QGroupBox, QSlider, QLineEdit,
                             QComboBox, QCheckBox, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPoint, QMetaObject
from PyQt5.QtGui import QPixmap, QImage, QPainter, QPen, QFont
from PIL import Image, ImageQt
import time
from PyQt5.QtWidgets import QApplication
import io

class ScreenDisplayWidget(QLabel):
    """屏幕显示组件"""
    
    # 定义信号
    screen_clicked = pyqtSignal(int, int)  # 屏幕点击信号
    screen_swiped = pyqtSignal(int, int, int, int)  # 屏幕滑动信号
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(300, 500)
        self.setScaledContents(True)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #333333;
                background-color: #000000;
            }
        """)
        
        # 鼠标操作相关
        self.mouse_pressed = False
        self.press_pos = QPoint()
        self.device_width = 1080
        self.device_height = 1920
        self.scale_x = 1.0
        self.scale_y = 1.0
        
        # 设置默认图片
        self.setText("设备屏幕\n\n请连接设备并选择设备")
        self.setAlignment(Qt.AlignCenter)
        self.setFont(QFont("Arial", 14))
        
    def update_screen(self, image: Image.Image):
        """更新屏幕显示"""
        try:
            if image and not self.isHidden():
                # 获取设备实际尺寸
                self.device_width, self.device_height = image.size
                
                # 转换为QPixmap，确保在主线程中执行
                def update_pixmap():
                    try:
                        # 转换PIL Image为QImage - 使用更高效的方法
                        # 将PIL图像转换为RGB格式（如果不是的话）
                        if image.mode != 'RGB':
                            rgb_image = image.convert('RGB')
                        else:
                            rgb_image = image
                        
                        # 获取图像数据
                        width, height = rgb_image.size
                        ptr = rgb_image.tobytes('raw', 'RGB')
                        
                        # 创建QImage
                        qimage = QImage(ptr, width, height, QImage.Format_RGB888)
                        
                        if qimage.isNull():
                            return
                        
                        # 从QImage创建QPixmap
                        pixmap = QPixmap.fromImage(qimage)
                        if pixmap.isNull():
                            return
                        
                        # 计算缩放比例
                        widget_size = self.size()
                        if widget_size.width() > 0 and widget_size.height() > 0:
                            self.scale_x = self.device_width / widget_size.width()
                            self.scale_y = self.device_height / widget_size.height()
                        
                        self.setPixmap(pixmap)
                    except Exception as e:
                        print(f"更新屏幕显示失败: {e}")
                
                # 确保在主线程中更新UI
                if QApplication.instance().thread() == self.thread():
                    update_pixmap()
                else:
                    QMetaObject.invokeMethod(self, "update_pixmap", Qt.QueuedConnection)
                
        except Exception as e:
            print(f"更新屏幕显示失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.mouse_pressed = True
            self.press_pos = event.pos()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.mouse_pressed:
            release_pos = event.pos()
            
            # 计算设备坐标
            device_x = int(release_pos.x() * self.scale_x)
            device_y = int(release_pos.y() * self.scale_y)
            
            # 判断是点击还是滑动
            distance = ((release_pos.x() - self.press_pos.x()) ** 2 + 
                       (release_pos.y() - self.press_pos.y()) ** 2) ** 0.5
            
            if distance < 10:  # 点击
                self.screen_clicked.emit(device_x, device_y)
            else:  # 滑动
                press_device_x = int(self.press_pos.x() * self.scale_x)
                press_device_y = int(self.press_pos.y() * self.scale_y)
                self.screen_swiped.emit(press_device_x, press_device_y, device_x, device_y)
            
            self.mouse_pressed = False

class ScreenControlWidget(QWidget):
    """屏幕控制组件"""
    
    # 定义信号
    key_pressed = pyqtSignal(str)
    text_sent = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        
        # 常用按键组
        key_group = QGroupBox("常用按键")
        key_layout = QHBoxLayout()
        
        # 创建常用按键
        keys = [
            ("HOME", "HOME"),
            ("BACK", "BACK"), 
            ("MENU", "MENU"),
            ("POWER", "POWER"),
            ("音量+", "VOLUME_UP"),
            ("音量-", "VOLUME_DOWN")
        ]
        
        self.key_buttons = {}
        for key_name, key_code in keys:
            btn = QPushButton(key_name)
            btn.clicked.connect(lambda checked, code=key_code: self.key_pressed.emit(code))
            key_layout.addWidget(btn)
            self.key_buttons[key_code] = btn
        
        key_group.setLayout(key_layout)
        layout.addWidget(key_group)
        
        # 文本输入组
        text_group = QGroupBox("文本输入")
        text_layout = QVBoxLayout()
        
        input_layout = QHBoxLayout()
        self.text_input = QLineEdit()
        self.text_input.setPlaceholderText("输入要发送的文本...")
        self.send_btn = QPushButton("发送")
        
        input_layout.addWidget(self.text_input)
        input_layout.addWidget(self.send_btn)
        
        text_layout.addLayout(input_layout)
        text_group.setLayout(text_layout)
        layout.addWidget(text_group)
        
        # 连接信号
        self.send_btn.clicked.connect(self.send_text)
        self.text_input.returnPressed.connect(self.send_text)
        
        self.setLayout(layout)
    
    def send_text(self):
        """发送文本"""
        text = self.text_input.text().strip()
        if text:
            self.text_sent.emit(text)
            self.text_input.clear()

class ScreenWidget(QWidget):
    """完整的屏幕组件，包含显示和控制"""
    
    def __init__(self):
        super().__init__()
        self.adb_manager = None
        self.current_device = None
        self.capture_timer = QTimer()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        
        # 屏幕显示区域
        self.screen_display = ScreenDisplayWidget()
        layout.addWidget(self.screen_display)
        
        # 控制区域
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Box)
        control_layout = QVBoxLayout()
        
        # 屏幕控制按钮
        screen_control_layout = QHBoxLayout()
        
        self.start_capture_btn = QPushButton("开始屏幕镜像")
        self.stop_capture_btn = QPushButton("停止屏幕镜像")
        self.screenshot_btn = QPushButton("截图")
        
        self.start_capture_btn.setEnabled(False)
        self.stop_capture_btn.setEnabled(False)
        self.screenshot_btn.setEnabled(False)
        
        screen_control_layout.addWidget(self.start_capture_btn)
        screen_control_layout.addWidget(self.stop_capture_btn)
        screen_control_layout.addWidget(self.screenshot_btn)
        screen_control_layout.addStretch()
        
        control_layout.addLayout(screen_control_layout)
        
        # 按键控制
        self.screen_control = ScreenControlWidget()
        control_layout.addWidget(self.screen_control)
        
        control_frame.setLayout(control_layout)
        layout.addWidget(control_frame)
        
        # 连接信号
        self.start_capture_btn.clicked.connect(self.start_screen_capture)
        self.stop_capture_btn.clicked.connect(self.stop_screen_capture)
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        
        self.screen_display.screen_clicked.connect(self.on_screen_clicked)
        self.screen_display.screen_swiped.connect(self.on_screen_swiped)
        
        self.screen_control.key_pressed.connect(self.on_key_pressed)
        self.screen_control.text_sent.connect(self.on_text_sent)
        
        self.setLayout(layout)
    
    def set_adb_manager(self, adb_manager):
        """设置ADB管理器"""
        self.adb_manager = adb_manager
    
    def set_current_device(self, serial: str):
        """设置当前设备"""
        self.current_device = serial
        
        # 更新按钮状态
        enabled = serial is not None and self.adb_manager is not None
        self.start_capture_btn.setEnabled(enabled)
        self.screenshot_btn.setEnabled(enabled)
        
        if not enabled:
            self.stop_screen_capture()
    
    def start_screen_capture(self):
        """开始屏幕捕获"""
        if not self.current_device or not self.adb_manager:
            return
        
        def on_screen_update(image):
            self.screen_display.update_screen(image)
        
        self.adb_manager.start_screen_capture(self.current_device, on_screen_update)
        
        self.start_capture_btn.setEnabled(False)
        self.stop_capture_btn.setEnabled(True)
    
    def stop_screen_capture(self):
        """停止屏幕捕获"""
        if self.adb_manager:
            self.adb_manager.stop_screen_capture()
        
        self.start_capture_btn.setEnabled(True)
        self.stop_capture_btn.setEnabled(False)
    
    def take_screenshot(self):
        """截图"""
        if not self.current_device or not self.adb_manager:
            return
        
        try:
            image = self.adb_manager.capture_screen(self.current_device)
            if image:
                # 保存截图
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"
                image.save(filename)
                print(f"截图已保存: {filename}")
        except Exception as e:
            print(f"截图失败: {e}")
    
    def on_screen_clicked(self, x, y):
        """屏幕点击事件"""
        if self.current_device and self.adb_manager:
            self.adb_manager.click_screen(self.current_device, x, y)
            print(f"点击屏幕: ({x}, {y})")
    
    def on_screen_swiped(self, x1, y1, x2, y2):
        """屏幕滑动事件"""
        if self.current_device and self.adb_manager:
            self.adb_manager.swipe_screen(self.current_device, x1, y1, x2, y2)
            print(f"滑动屏幕: ({x1}, {y1}) -> ({x2}, {y2})")
    
    def on_key_pressed(self, key_code):
        """按键事件"""
        if self.current_device and self.adb_manager:
            self.adb_manager.send_key(self.current_device, key_code)
            print(f"发送按键: {key_code}")
    
    def on_text_sent(self, text):
        """文本发送事件"""
        if self.current_device and self.adb_manager:
            self.adb_manager.send_text(self.current_device, text)
            print(f"发送文本: {text}") 