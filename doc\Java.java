C:\Users\<USER>\Desktop\Android>frida-ps -Ua
  PID  Name       Identifier
-----  ---------  -------------------------------
16592  <USER>  <GROUP>.kitsunebi.kitsunebi4android
15890  Search     com.android.quicksearchbox
16124  微信         com.tencent.mm
16203  日历         com.android.calendar
15783  时钟         com.android.deskclock
16142  电话         com.android.dialer
16243  相机         com.android.camera2
15955  短信         com.android.messaging
15472  设置         com.android.settings
16268  通讯录        com.android.contacts

C:\Users\<USER>\Desktop\Android>frida -U 16124 -l "s
     ____
    / _  |   Frida 17.2.11 - A world-class dynamic instrume
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'ob
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://frida.re/docs/home/
   . . . .
   . . . .   Connected to OP5929L1 (id=11FAFS00000WA9)
Attaching...
[+] 微信消息发送监听脚本启动
[+] 开始监听关键方法调用
[+] 监听et0.o.i方法，重载数量: 1
[+] 重载 0 参数类型: Lvp4/z;, Lyp4/b;, Lkotlin/coroutines/C
[+] 监听脚本加载完成
[+] 现在可以在微信中发送消息来观察调用流程
[+] 观察各个参数的类型和值变化
[+] 微信消息发送监听脚本已加载
[+] 纯监听模式，用于分析消息发送流程
[OP5929L1::PID::16124 ]-> [+] 配置对象toString被调用
██████████████████████████████
[+] 消息对象构造函数 0 被调用
[+] 参数数量: 0
[+] 消息对象构造完成
██████████████████████████████
██████████████████████████████
[+] 消息对象构造函数 0 被调用
[+] 参数数量: 0
[+] 消息对象构造完成
██████████████████████████████
══════════════════════════════
[+] et0.o.i 方法被调用
[+] 参数数量: 3
[+] 参数 0:
    类型: vp4.z
    值: vp4.z@4299e7e
[+] 参数 1:
    类型: et0.e
    值: et0.e@c0c3a91
[+] 参数 2:TypeError: not a function
    at <anonymous> (C:\Users\<USER>\Desktop\Android\
    at apply (native)
    at <anonymous> (/frida/bridges/java.js:8)
    at <anonymous> (/frida/bridges/java.js:8)

Process terminated
[OP5929L1::PID::16124 ]->

Thank you for using Frida!

C:\Users\<USER>\Desktop\Android>