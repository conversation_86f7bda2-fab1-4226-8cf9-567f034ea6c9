package p713qk;

import android.content.ContentValues;
import android.database.Cursor;
import aq4.AbstractC4001f0;
import aq4.C3999e0;
import com.tencent.p087mm.sdk.platformtools.AbstractC59537n2;
import com.tencent.p087mm.sdk.platformtools.AbstractC59663z;
import com.tencent.p087mm.sdk.platformtools.C59449f2;
import com.tencent.youtu.sdkkitframework.common.StateEvent;
import dq4.C72101e;
import eq4.C76492a;
import eq4.C76513i0;
import java.io.IOException;
import java.lang.reflect.Field;
import kotlin.jvm.internal.AbstractC100682o;
import mw0.AbstractC109779f;
import org.json.JSONObject;
import sn4.p04;

/* renamed from: qk.t7 */
/* loaded from: classes9.dex */
public class C127133t7 extends AbstractC4001f0 {

    /* renamed from: A1 */
    public static final int f357032A1;

    /* renamed from: B1 */
    public static final int f357033B1;

    /* renamed from: C1 */
    public static final int f357034C1;

    /* renamed from: D1 */
    public static final int f357035D1;

    /* renamed from: E1 */
    public static final int f357036E1;

    /* renamed from: F1 */
    public static final int f357037F1;

    /* renamed from: G1 */
    public static final int f357038G1;

    /* renamed from: H1 */
    public static final int f357039H1;

    /* renamed from: I1 */
    public static final int f357040I1;

    /* renamed from: J1 */
    public static final int f357041J1;

    /* renamed from: K1 */
    public static final int f357042K1;

    /* renamed from: L1 */
    public static final int f357043L1;

    /* renamed from: M1 */
    public static final int f357044M1;

    /* renamed from: N1 */
    public static final int f357045N1;

    /* renamed from: O1 */
    public static final int f357046O1;

    /* renamed from: P1 */
    public static final int f357047P1;

    /* renamed from: Q1 */
    public static final C3999e0 f357048Q1;

    /* renamed from: R1 */
    public static final C72101e f357049R1;

    /* renamed from: l1 */
    public static final C76492a f357050l1;

    /* renamed from: m1 */
    public static final C76492a f357051m1;

    /* renamed from: n1 */
    public static final C76492a f357052n1;

    /* renamed from: o1 */
    public static final C76492a f357053o1;

    /* renamed from: p1 */
    public static final C76492a f357054p1;

    /* renamed from: q1 */
    public static final C76492a f357055q1;

    /* renamed from: r1 */
    public static final int f357056r1;

    /* renamed from: s1 */
    public static final int f357057s1;

    /* renamed from: t1 */
    public static final int f357058t1;

    /* renamed from: u1 */
    public static final int f357059u1;

    /* renamed from: v1 */
    public static final int f357060v1;

    /* renamed from: w1 */
    public static final int f357061w1;

    /* renamed from: x1 */
    public static final int f357062x1;

    /* renamed from: y0 */
    public static final C76513i0 f357063y0;

    /* renamed from: y1 */
    public static final int f357064y1;

    /* renamed from: z1 */
    public static final int f357065z1;

    /* renamed from: E */
    public String f357070E;

    /* renamed from: F */
    public int f357071F;

    /* renamed from: G */
    public String f357072G;

    /* renamed from: H */
    public int f357073H;

    /* renamed from: I */
    public int f357074I;

    /* renamed from: J */
    public int f357075J;

    /* renamed from: K */
    public int f357076K;

    /* renamed from: L */
    public int f357077L;

    /* renamed from: M */
    public int f357078M;

    /* renamed from: N */
    public String f357079N;

    /* renamed from: P */
    public String f357080P;

    /* renamed from: Q */
    public String f357081Q;

    /* renamed from: R */
    public int f357082R;

    /* renamed from: S */
    public String f357083S;

    /* renamed from: T */
    public byte[] f357084T;

    /* renamed from: U */
    public String f357085U;

    /* renamed from: V */
    public String f357086V;

    /* renamed from: W */
    public int f357087W;

    /* renamed from: X */
    public int f357088X;

    /* renamed from: Y */
    public int f357089Y;

    /* renamed from: Z */
    public int f357090Z;
    private long field_bizChatId;
    private String field_bizChatUserId;
    private String field_bizClientMsgId;
    private String field_content;
    private long field_createTime;
    private int field_flag;
    private String field_fromUsername;
    private String field_historyId;
    private String field_imgPath;
    private int field_isSend;
    private int field_isShowTimer;
    private byte[] field_lvbuffer;
    private long field_msgId;
    private long field_msgSeq;
    private long field_msgSvrId;
    private String field_reserved;
    private p04 field_solitaireFoldInfo;
    private int field_status;
    private String field_talker;
    private int field_talkerId;
    private String field_toUsername;
    private String field_transBrandWording;
    private String field_transContent;
    private int field_type;

    /* renamed from: p0 */
    public int f357101p0;

    /* renamed from: x0 */
    public String f357110x0;

    /* renamed from: d */
    public boolean f357091d = false;

    /* renamed from: e */
    public boolean f357092e = false;

    /* renamed from: f */
    public boolean f357093f = false;

    /* renamed from: g */
    public boolean f357094g = false;

    /* renamed from: h */
    public boolean f357095h = false;

    /* renamed from: i */
    public boolean f357096i = false;

    /* renamed from: m */
    public boolean f357097m = false;

    /* renamed from: n */
    public boolean f357098n = false;

    /* renamed from: o */
    public boolean f357099o = false;

    /* renamed from: p */
    public boolean f357100p = false;

    /* renamed from: q */
    public boolean f357102q = false;

    /* renamed from: r */
    public boolean f357103r = false;

    /* renamed from: s */
    public boolean f357104s = false;

    /* renamed from: t */
    public boolean f357105t = false;

    /* renamed from: u */
    public boolean f357106u = false;

    /* renamed from: v */
    public boolean f357107v = false;

    /* renamed from: w */
    public boolean f357108w = false;

    /* renamed from: x */
    public boolean f357109x = false;

    /* renamed from: y */
    public boolean f357111y = false;

    /* renamed from: z */
    public boolean f357112z = false;

    /* renamed from: A */
    public boolean f357066A = false;

    /* renamed from: B */
    public boolean f357067B = false;

    /* renamed from: C */
    public boolean f357068C = false;

    /* renamed from: D */
    public boolean f357069D = false;

    static {
        C76513i0 c76513i0 = new C76513i0(StateEvent.Name.MESSAGE);
        f357063y0 = c76513i0;
        String tableName = c76513i0.f239407a;
        AbstractC100682o.m79781g(tableName, "tableName");
        f357050l1 = new C76492a("msgId", "long", tableName, "");
        AbstractC100682o.m79781g(tableName, "tableName");
        f357051m1 = new C76492a("type", "int", tableName, "");
        AbstractC100682o.m79781g(tableName, "tableName");
        f357052n1 = new C76492a("isSend", "int", tableName, "");
        AbstractC100682o.m79781g(tableName, "tableName");
        f357053o1 = new C76492a("createTime", "long", tableName, "");
        f357054p1 = new C76492a("talker", "string", tableName, "");
        f357055q1 = new C76492a("content", "string", tableName, "");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        AbstractC100682o.m79781g(tableName, "tableName");
        f357056r1 = 104191100;
        f357057s1 = -1294411543;
        f357058t1 = 3575610;
        f357059u1 = -892481550;
        f357060v1 = -1180128302;
        f357061w1 = -735390658;
        f357062x1 = 1369213417;
        f357064y1 = -881080743;
        f357065z1 = 951530617;
        f357032A1 = 1916786312;
        f357033B1 = -350385368;
        f357034C1 = -486944182;
        f357035D1 = -610034348;
        f357036E1 = 1437142193;
        f357037F1 = 522984921;
        f357038G1 = 1298065502;
        f357039H1 = 193082758;
        f357040I1 = -2109920975;
        f357041J1 = -1065033442;
        f357042K1 = 3145580;
        f357043L1 = 60550149;
        f357044M1 = 1604237984;
        f357045N1 = -285781903;
        f357046O1 = 1725503695;
        f357047P1 = 108705909;
        f357048Q1 = initAutoDBInfo(C127133t7.class);
        f357049R1 = new C72101e();
    }

    public static C3999e0 initAutoDBInfo(Class cls) {
        C3999e0 c3999e0 = new C3999e0();
        c3999e0.f10465a = new Field[24];
        String[] strArr = new String[25];
        c3999e0.f10467c = strArr;
        strArr[0] = "msgId";
        c3999e0.f10468d.put("msgId", "LONG PRIMARY KEY ");
        c3999e0.f10466b = "msgId";
        c3999e0.f10467c[1] = "msgSvrId";
        c3999e0.f10468d.put("msgSvrId", "LONG");
        c3999e0.f10467c[2] = "type";
        c3999e0.f10468d.put("type", "INTEGER");
        c3999e0.f10467c[3] = "status";
        c3999e0.f10468d.put("status", "INTEGER");
        c3999e0.f10467c[4] = "isSend";
        c3999e0.f10468d.put("isSend", "INTEGER");
        c3999e0.f10467c[5] = "isShowTimer";
        c3999e0.f10468d.put("isShowTimer", "INTEGER");
        c3999e0.f10467c[6] = "createTime";
        c3999e0.f10468d.put("createTime", "LONG");
        c3999e0.f10467c[7] = "talker";
        c3999e0.f10468d.put("talker", "TEXT");
        c3999e0.f10467c[8] = "content";
        c3999e0.f10468d.put("content", "TEXT default '' ");
        c3999e0.f10467c[9] = "imgPath";
        c3999e0.f10468d.put("imgPath", "TEXT");
        c3999e0.f10467c[10] = "reserved";
        c3999e0.f10468d.put("reserved", "TEXT");
        c3999e0.f10467c[11] = "lvbuffer";
        c3999e0.f10468d.put("lvbuffer", "BLOB");
        c3999e0.f10467c[12] = "talkerId";
        c3999e0.f10468d.put("talkerId", "INTEGER");
        c3999e0.f10467c[13] = "transContent";
        c3999e0.f10468d.put("transContent", "TEXT default '' ");
        c3999e0.f10467c[14] = "transBrandWording";
        c3999e0.f10468d.put("transBrandWording", "TEXT default '' ");
        c3999e0.f10467c[15] = "bizClientMsgId";
        c3999e0.f10468d.put("bizClientMsgId", "TEXT default '' ");
        c3999e0.f10467c[16] = "bizChatId";
        c3999e0.f10468d.put("bizChatId", "LONG default '-1' ");
        c3999e0.f10467c[17] = "bizChatUserId";
        c3999e0.f10468d.put("bizChatUserId", "TEXT default '' ");
        c3999e0.f10467c[18] = "msgSeq";
        c3999e0.f10468d.put("msgSeq", "LONG");
        c3999e0.f10467c[19] = "flag";
        c3999e0.f10468d.put("flag", "INTEGER default '0' ");
        c3999e0.f10467c[20] = "solitaireFoldInfo";
        c3999e0.f10468d.put("solitaireFoldInfo", "BLOB");
        c3999e0.f10467c[21] = "fromUsername";
        c3999e0.f10468d.put("fromUsername", "TEXT");
        c3999e0.f10467c[22] = "toUsername";
        c3999e0.f10468d.put("toUsername", "TEXT");
        c3999e0.f10467c[23] = "historyId";
        c3999e0.f10468d.put("historyId", "TEXT");
        c3999e0.f10467c[24] = "rowid";
        c3999e0.f10469e = " msgId LONG PRIMARY KEY ,  msgSvrId LONG,  type INTEGER,  status INTEGER,  isSend INTEGER,  isShowTimer INTEGER,  createTime LONG,  talker TEXT,  content TEXT default '' ,  imgPath TEXT,  reserved TEXT,  lvbuffer BLOB,  talkerId INTEGER,  transContent TEXT default '' ,  transBrandWording TEXT default '' ,  bizClientMsgId TEXT default '' ,  bizChatId LONG default '-1' ,  bizChatUserId TEXT default '' ,  msgSeq LONG,  flag INTEGER default '0' ,  solitaireFoldInfo BLOB,  fromUsername TEXT,  toUsername TEXT,  historyId TEXT";
        if (c3999e0.f10466b == null) {
            c3999e0.f10466b = "rowid";
        }
        return c3999e0;
    }

    /* renamed from: A1 */
    public void m94383A1(String str) {
        this.field_reserved = str;
        this.f357102q = true;
    }

    /* renamed from: B1 */
    public final void m94384B1(p04 p04Var) {
        this.field_solitaireFoldInfo = p04Var;
        this.f357066A = true;
    }

    /* renamed from: C1 */
    public void mo54074C1(int i16) {
        this.field_status = i16;
        this.f357094g = true;
    }

    /* renamed from: D0 */
    public String m94385D0() {
        return this.field_imgPath;
    }

    /* renamed from: E0 */
    public int m94386E0() {
        return this.field_isSend;
    }

    /* renamed from: F0 */
    public int m94387F0() {
        return this.field_isShowTimer;
    }

    /* renamed from: H0 */
    public byte[] m94388H0() {
        return this.field_lvbuffer;
    }

    /* renamed from: I0 */
    public long m94389I0() {
        return this.field_msgSeq;
    }

    /* renamed from: J0 */
    public String m94390J0() {
        return this.f357072G;
    }

    /* renamed from: J1 */
    public void m94391J1(String str) {
        this.field_talker = str;
        this.f357098n = true;
    }

    /* renamed from: K0 */
    public long m94392K0() {
        return this.field_msgSvrId;
    }

    /* renamed from: K1 */
    public void m94393K1(int i16) {
        this.field_talkerId = i16;
        this.f357104s = true;
    }

    /* renamed from: M0 */
    public String m94394M0() {
        return this.field_reserved;
    }

    /* renamed from: M1 */
    public void m94395M1(String str) {
        this.field_transBrandWording = str;
        this.f357106u = true;
    }

    /* renamed from: N0 */
    public final p04 m94396N0() {
        return this.field_solitaireFoldInfo;
    }

    /* renamed from: O0 */
    public int m94397O0() {
        return this.field_status;
    }

    /* renamed from: O1 */
    public void m94398O1(String str) {
        this.field_transContent = str;
        this.f357105t = true;
    }

    /* renamed from: P0 */
    public String m94399P0() {
        return this.field_talker;
    }

    /* renamed from: Q0 */
    public int m94400Q0() {
        return this.field_talkerId;
    }

    /* renamed from: T0 */
    public String mo54094T0() {
        return this.field_toUsername;
    }

    /* renamed from: V0 */
    public String m94401V0() {
        return this.field_transBrandWording;
    }

    /* renamed from: W0 */
    public String m94402W0() {
        return this.field_transContent;
    }

    /* renamed from: Y0 */
    public void m94403Y0(long j16) {
        this.field_bizChatId = j16;
        this.f357108w = true;
    }

    /* renamed from: Z0 */
    public void m94404Z0(String str) {
        this.field_bizChatUserId = str;
        this.f357109x = true;
    }

    /* renamed from: a1 */
    public void m94405a1(String str) {
        this.field_bizClientMsgId = str;
        this.f357107v = true;
    }

    /* renamed from: b1 */
    public void m94406b1(String str) {
        this.f357080P = str;
        this.f357103r = true;
    }

    public boolean compareContent(AbstractC4001f0 abstractC4001f0) {
        if (abstractC4001f0 == null || !(abstractC4001f0 instanceof C127133t7)) {
            return false;
        }
        C127133t7 c127133t7 = (C127133t7) abstractC4001f0;
        return AbstractC109779f.m84327a(Long.valueOf(this.field_msgId), Long.valueOf(c127133t7.field_msgId)) && AbstractC109779f.m84327a(Long.valueOf(this.field_msgSvrId), Long.valueOf(c127133t7.field_msgSvrId)) && AbstractC109779f.m84327a(Integer.valueOf(this.field_type), Integer.valueOf(c127133t7.field_type)) && AbstractC109779f.m84327a(Integer.valueOf(this.field_status), Integer.valueOf(c127133t7.field_status)) && AbstractC109779f.m84327a(Integer.valueOf(this.field_isSend), Integer.valueOf(c127133t7.field_isSend)) && AbstractC109779f.m84327a(Integer.valueOf(this.field_isShowTimer), Integer.valueOf(c127133t7.field_isShowTimer)) && AbstractC109779f.m84327a(Long.valueOf(this.field_createTime), Long.valueOf(c127133t7.field_createTime)) && AbstractC109779f.m84327a(this.field_talker, c127133t7.field_talker) && AbstractC109779f.m84327a(this.field_content, c127133t7.field_content) && AbstractC109779f.m84327a(this.field_imgPath, c127133t7.field_imgPath) && AbstractC109779f.m84327a(this.field_reserved, c127133t7.field_reserved) && AbstractC109779f.m84327a(this.field_lvbuffer, c127133t7.field_lvbuffer) && AbstractC109779f.m84327a(Integer.valueOf(this.field_talkerId), Integer.valueOf(c127133t7.field_talkerId)) && AbstractC109779f.m84327a(this.field_transContent, c127133t7.field_transContent) && AbstractC109779f.m84327a(this.field_transBrandWording, c127133t7.field_transBrandWording) && AbstractC109779f.m84327a(this.field_bizClientMsgId, c127133t7.field_bizClientMsgId) && AbstractC109779f.m84327a(Long.valueOf(this.field_bizChatId), Long.valueOf(c127133t7.field_bizChatId)) && AbstractC109779f.m84327a(this.field_bizChatUserId, c127133t7.field_bizChatUserId) && AbstractC109779f.m84327a(Long.valueOf(this.field_msgSeq), Long.valueOf(c127133t7.field_msgSeq)) && AbstractC109779f.m84327a(Integer.valueOf(this.field_flag), Integer.valueOf(c127133t7.field_flag)) && AbstractC109779f.m84327a(this.field_solitaireFoldInfo, c127133t7.field_solitaireFoldInfo) && AbstractC109779f.m84327a(this.field_fromUsername, c127133t7.field_fromUsername) && AbstractC109779f.m84327a(this.field_toUsername, c127133t7.field_toUsername) && AbstractC109779f.m84327a(this.field_historyId, c127133t7.field_historyId);
    }

    /* JADX DEBUG: Don't trust debug lines info. Lines numbers was adjusted: min line is 1 */
    @Override // aq4.AbstractC4001f0
    public void convertFrom(Cursor cursor) {
        String[] columnNames = cursor.getColumnNames();
        if (columnNames == null) {
            return;
        }
        int length = columnNames.length;
        for (int i16 = 0; i16 < length; i16++) {
            int iHashCode = columnNames[i16].hashCode();
            if (f357056r1 == iHashCode) {
                try {
                    this.field_msgId = cursor.getLong(i16);
                    this.f357091d = true;
                } catch (Throwable th6) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th6, "convertFrom %s", columnNames[i16]);
                    String str = AbstractC59663z.f190530a;
                }
            } else if (f357057s1 == iHashCode) {
                try {
                    this.field_msgSvrId = cursor.getLong(i16);
                } catch (Throwable th7) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th7, "convertFrom %s", columnNames[i16]);
                    String str2 = AbstractC59663z.f190530a;
                }
            } else if (f357058t1 == iHashCode) {
                try {
                    this.field_type = cursor.getInt(i16);
                } catch (Throwable th8) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th8, "convertFrom %s", columnNames[i16]);
                    String str3 = AbstractC59663z.f190530a;
                }
            } else if (f357059u1 == iHashCode) {
                try {
                    this.field_status = cursor.getInt(i16);
                } catch (Throwable th9) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th9, "convertFrom %s", columnNames[i16]);
                    String str4 = AbstractC59663z.f190530a;
                }
            } else if (f357060v1 == iHashCode) {
                try {
                    this.field_isSend = cursor.getInt(i16);
                } catch (Throwable th10) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th10, "convertFrom %s", columnNames[i16]);
                    String str5 = AbstractC59663z.f190530a;
                }
            } else if (f357061w1 == iHashCode) {
                try {
                    this.field_isShowTimer = cursor.getInt(i16);
                } catch (Throwable th11) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th11, "convertFrom %s", columnNames[i16]);
                    String str6 = AbstractC59663z.f190530a;
                }
            } else if (f357062x1 == iHashCode) {
                try {
                    this.field_createTime = cursor.getLong(i16);
                } catch (Throwable th12) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th12, "convertFrom %s", columnNames[i16]);
                    String str7 = AbstractC59663z.f190530a;
                }
            } else if (f357064y1 == iHashCode) {
                try {
                    this.field_talker = cursor.getString(i16);
                } catch (Throwable th13) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th13, "convertFrom %s", columnNames[i16]);
                    String str8 = AbstractC59663z.f190530a;
                }
            } else if (f357065z1 == iHashCode) {
                try {
                    this.field_content = cursor.getString(i16);
                } catch (Throwable th14) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th14, "convertFrom %s", columnNames[i16]);
                    String str9 = AbstractC59663z.f190530a;
                }
            } else if (f357032A1 == iHashCode) {
                try {
                    this.field_imgPath = cursor.getString(i16);
                } catch (Throwable th15) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th15, "convertFrom %s", columnNames[i16]);
                    String str10 = AbstractC59663z.f190530a;
                }
            } else if (f357033B1 == iHashCode) {
                try {
                    this.field_reserved = cursor.getString(i16);
                } catch (Throwable th16) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th16, "convertFrom %s", columnNames[i16]);
                    String str11 = AbstractC59663z.f190530a;
                }
            } else if (f357034C1 == iHashCode) {
                try {
                    this.field_lvbuffer = cursor.getBlob(i16);
                } catch (Throwable th17) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th17, "convertFrom %s", columnNames[i16]);
                    String str12 = AbstractC59663z.f190530a;
                }
            } else if (f357035D1 == iHashCode) {
                try {
                    this.field_talkerId = cursor.getInt(i16);
                } catch (Throwable th18) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th18, "convertFrom %s", columnNames[i16]);
                    String str13 = AbstractC59663z.f190530a;
                }
            } else if (f357036E1 == iHashCode) {
                try {
                    this.field_transContent = cursor.getString(i16);
                } catch (Throwable th19) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th19, "convertFrom %s", columnNames[i16]);
                    String str14 = AbstractC59663z.f190530a;
                }
            } else if (f357037F1 == iHashCode) {
                try {
                    this.field_transBrandWording = cursor.getString(i16);
                } catch (Throwable th20) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th20, "convertFrom %s", columnNames[i16]);
                    String str15 = AbstractC59663z.f190530a;
                }
            } else if (f357038G1 == iHashCode) {
                try {
                    this.field_bizClientMsgId = cursor.getString(i16);
                } catch (Throwable th21) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th21, "convertFrom %s", columnNames[i16]);
                    String str16 = AbstractC59663z.f190530a;
                }
            } else if (f357039H1 == iHashCode) {
                try {
                    this.field_bizChatId = cursor.getLong(i16);
                } catch (Throwable th22) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th22, "convertFrom %s", columnNames[i16]);
                    String str17 = AbstractC59663z.f190530a;
                }
            } else if (f357040I1 == iHashCode) {
                try {
                    this.field_bizChatUserId = cursor.getString(i16);
                } catch (Throwable th23) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th23, "convertFrom %s", columnNames[i16]);
                    String str18 = AbstractC59663z.f190530a;
                }
            } else if (f357041J1 == iHashCode) {
                try {
                    this.field_msgSeq = cursor.getLong(i16);
                } catch (Throwable th24) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th24, "convertFrom %s", columnNames[i16]);
                    String str19 = AbstractC59663z.f190530a;
                }
            } else if (f357042K1 == iHashCode) {
                try {
                    this.field_flag = cursor.getInt(i16);
                } catch (Throwable th25) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th25, "convertFrom %s", columnNames[i16]);
                    String str20 = AbstractC59663z.f190530a;
                }
            } else if (f357043L1 == iHashCode) {
                try {
                    byte[] blob = cursor.getBlob(i16);
                    if (blob != null && blob.length > 0) {
                        this.field_solitaireFoldInfo = (p04) new p04().parseFrom(blob);
                    }
                } catch (Throwable th26) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th26, "convertFrom %s", columnNames[i16]);
                    String str21 = AbstractC59663z.f190530a;
                }
            } else if (f357044M1 == iHashCode) {
                try {
                    this.field_fromUsername = cursor.getString(i16);
                } catch (Throwable th27) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th27, "convertFrom %s", columnNames[i16]);
                    String str22 = AbstractC59663z.f190530a;
                }
            } else if (f357045N1 == iHashCode) {
                try {
                    this.field_toUsername = cursor.getString(i16);
                } catch (Throwable th28) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th28, "convertFrom %s", columnNames[i16]);
                    String str23 = AbstractC59663z.f190530a;
                }
            } else if (f357046O1 == iHashCode) {
                try {
                    this.field_historyId = cursor.getString(i16);
                } catch (Throwable th29) {
                    AbstractC59537n2.m53233n("MicroMsg.SDK.BaseMsgInfo", th29, "convertFrom %s", columnNames[i16]);
                    String str24 = AbstractC59663z.f190530a;
                }
            } else if (f357047P1 == iHashCode) {
                this.systemRowid = cursor.getLong(i16);
            }
        }
        try {
            byte[] bArr = this.field_lvbuffer;
            if (bArr != null && bArr.length != 0) {
                C59449f2 c59449f2 = new C59449f2();
                int iM53098i = c59449f2.m53098i(bArr);
                if (iM53098i != 0) {
                    AbstractC59537n2.m53224e("MicroMsg.SDK.BaseMsgInfo", "parse LVBuffer error:" + iM53098i, null);
                    return;
                }
                if (!c59449f2.m53092c()) {
                    this.f357070E = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357071F = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357072G = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357073H = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357074I = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357075J = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357076K = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357077L = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357078M = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357079N = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357080P = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357081Q = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357082R = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357083S = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357084T = c59449f2.m53093d();
                }
                if (!c59449f2.m53092c()) {
                    this.f357085U = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357086V = c59449f2.m53096g();
                }
                if (!c59449f2.m53092c()) {
                    this.f357087W = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357088X = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357089Y = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357090Z = c59449f2.m53094e();
                }
                if (!c59449f2.m53092c()) {
                    this.f357101p0 = c59449f2.m53094e();
                }
                if (c59449f2.m53092c()) {
                    return;
                }
                this.f357110x0 = c59449f2.m53096g();
            }
        } catch (Exception unused) {
            AbstractC59537n2.m53224e("MicroMsg.SDK.BaseMsgInfo", "get value failed", null);
        }
    }

    @Override // aq4.AbstractC4001f0
    public ContentValues convertTo() {
        p04 p04Var;
        try {
            if (this.f357103r) {
                C59449f2 c59449f2 = new C59449f2();
                c59449f2.m53097h();
                c59449f2.m53102m(this.f357070E);
                c59449f2.m53100k(this.f357071F);
                c59449f2.m53102m(this.f357072G);
                c59449f2.m53100k(this.f357073H);
                c59449f2.m53100k(this.f357074I);
                c59449f2.m53100k(this.f357075J);
                c59449f2.m53100k(this.f357076K);
                c59449f2.m53100k(this.f357077L);
                c59449f2.m53100k(this.f357078M);
                c59449f2.m53102m(this.f357079N);
                c59449f2.m53102m(this.f357080P);
                c59449f2.m53102m(this.f357081Q);
                c59449f2.m53100k(this.f357082R);
                c59449f2.m53102m(this.f357083S);
                c59449f2.m53099j(this.f357084T);
                c59449f2.m53102m(this.f357085U);
                c59449f2.m53102m(this.f357086V);
                c59449f2.m53100k(this.f357087W);
                c59449f2.m53100k(this.f357088X);
                c59449f2.m53100k(this.f357089Y);
                c59449f2.m53100k(this.f357090Z);
                c59449f2.m53100k(this.f357101p0);
                c59449f2.m53102m(this.f357110x0);
                this.field_lvbuffer = c59449f2.m53090a();
            }
        } catch (Exception e16) {
            AbstractC59537n2.m53224e("MicroMsg.SDK.BaseMsgInfo", "get value failed, %s", e16.getMessage());
        }
        ContentValues contentValues = new ContentValues();
        if (this.f357091d) {
            contentValues.put("msgId", Long.valueOf(this.field_msgId));
        }
        if (this.f357092e) {
            contentValues.put("msgSvrId", Long.valueOf(this.field_msgSvrId));
        }
        if (this.f357093f) {
            contentValues.put("type", Integer.valueOf(this.field_type));
        }
        if (this.f357094g) {
            contentValues.put("status", Integer.valueOf(this.field_status));
        }
        if (this.f357095h) {
            contentValues.put("isSend", Integer.valueOf(this.field_isSend));
        }
        if (this.f357096i) {
            contentValues.put("isShowTimer", Integer.valueOf(this.field_isShowTimer));
        }
        if (this.f357097m) {
            contentValues.put("createTime", Long.valueOf(this.field_createTime));
        }
        if (this.f357098n) {
            contentValues.put("talker", this.field_talker);
        }
        if (this.field_content == null) {
            this.field_content = "";
        }
        if (this.f357099o) {
            contentValues.put("content", this.field_content);
        }
        if (this.f357100p) {
            contentValues.put("imgPath", this.field_imgPath);
        }
        if (this.f357102q) {
            contentValues.put("reserved", this.field_reserved);
        }
        if (this.f357103r) {
            contentValues.put("lvbuffer", this.field_lvbuffer);
        }
        if (this.f357104s) {
            contentValues.put("talkerId", Integer.valueOf(this.field_talkerId));
        }
        if (this.field_transContent == null) {
            this.field_transContent = "";
        }
        if (this.f357105t) {
            contentValues.put("transContent", this.field_transContent);
        }
        if (this.field_transBrandWording == null) {
            this.field_transBrandWording = "";
        }
        if (this.f357106u) {
            contentValues.put("transBrandWording", this.field_transBrandWording);
        }
        if (this.field_bizClientMsgId == null) {
            this.field_bizClientMsgId = "";
        }
        if (this.f357107v) {
            contentValues.put("bizClientMsgId", this.field_bizClientMsgId);
        }
        if (this.f357108w) {
            contentValues.put("bizChatId", Long.valueOf(this.field_bizChatId));
        }
        if (this.field_bizChatUserId == null) {
            this.field_bizChatUserId = "";
        }
        if (this.f357109x) {
            contentValues.put("bizChatUserId", this.field_bizChatUserId);
        }
        if (this.f357111y) {
            contentValues.put("msgSeq", Long.valueOf(this.field_msgSeq));
        }
        if (this.f357112z) {
            contentValues.put("flag", Integer.valueOf(this.field_flag));
        }
        if (this.f357066A && (p04Var = this.field_solitaireFoldInfo) != null) {
            try {
                contentValues.put("solitaireFoldInfo", p04Var.toByteArray());
            } catch (IOException e17) {
                AbstractC59537n2.m53224e("MicroMsg.SDK.BaseMsgInfo", e17.getMessage(), null);
            }
        }
        if (this.f357067B) {
            contentValues.put("fromUsername", this.field_fromUsername);
        }
        if (this.f357068C) {
            contentValues.put("toUsername", this.field_toUsername);
        }
        if (this.f357069D) {
            contentValues.put("historyId", this.field_historyId);
        }
        long j16 = this.systemRowid;
        if (j16 > 0) {
            contentValues.put("rowid", Long.valueOf(j16));
        }
        return contentValues;
    }

    /* renamed from: d1 */
    public void m94407d1(String str) {
        this.field_content = str;
        this.f357099o = true;
    }

    /* renamed from: e1 */
    public void m94408e1(long j16) {
        this.field_createTime = j16;
        this.f357097m = true;
    }

    /* renamed from: f1 */
    public void m94409f1(int i16) {
        this.field_flag = i16;
        this.f357112z = true;
    }

    public String getContent() {
        return this.field_content;
    }

    public long getCreateTime() {
        return this.field_createTime;
    }

    @Override // aq4.AbstractC4001f0
    public C3999e0 getDBInfo() {
        return f357048Q1;
    }

    public long getMsgId() {
        return this.field_msgId;
    }

    @Override // aq4.AbstractC4001f0
    public C72101e getObserverOwner() {
        return f357049R1;
    }

    @Override // aq4.AbstractC4001f0
    public Object getPrimaryKeyValue() {
        return Long.valueOf(this.field_msgId);
    }

    @Override // aq4.AbstractC4001f0
    public C76513i0 getTable() {
        return f357063y0;
    }

    @Override // aq4.AbstractC4001f0
    public String getTableName() {
        return f357063y0.f239407a;
    }

    public int getType() {
        return this.field_type;
    }

    /* renamed from: h1 */
    public void m94410h1(String str) {
        this.field_historyId = str;
        this.f357069D = true;
    }

    /* renamed from: j1 */
    public void m94411j1(String str) {
        this.field_imgPath = str;
        this.f357100p = true;
    }

    /* renamed from: n1 */
    public void m94412n1(int i16) {
        this.field_isSend = i16;
        this.f357095h = true;
    }

    /* renamed from: p1 */
    public void m94413p1(int i16) {
        this.field_isShowTimer = i16;
        this.f357096i = true;
    }

    /* renamed from: r1 */
    public void m94414r1(byte[] bArr) {
        this.field_lvbuffer = bArr;
        this.f357103r = true;
    }

    public void setMsgId(long j16) {
        this.field_msgId = j16;
        this.f357091d = true;
    }

    public void setType(int i16) {
        this.field_type = i16;
        this.f357093f = true;
    }

    /* renamed from: t0 */
    public long m94415t0() {
        return this.field_bizChatId;
    }

    @Override // aq4.AbstractC4001f0
    public Object toJSON() {
        JSONObject jSONObject = new JSONObject();
        try {
            AbstractC109779f.m84328b(jSONObject, "msgId", Long.valueOf(this.field_msgId));
            AbstractC109779f.m84328b(jSONObject, "msgSvrId", Long.valueOf(this.field_msgSvrId));
            AbstractC109779f.m84328b(jSONObject, "type", Integer.valueOf(this.field_type));
            AbstractC109779f.m84328b(jSONObject, "status", Integer.valueOf(this.field_status));
            AbstractC109779f.m84328b(jSONObject, "isSend", Integer.valueOf(this.field_isSend));
            AbstractC109779f.m84328b(jSONObject, "isShowTimer", Integer.valueOf(this.field_isShowTimer));
            AbstractC109779f.m84328b(jSONObject, "createTime", Long.valueOf(this.field_createTime));
            AbstractC109779f.m84328b(jSONObject, "talker", this.field_talker);
            AbstractC109779f.m84328b(jSONObject, "content", this.field_content);
            AbstractC109779f.m84328b(jSONObject, "imgPath", this.field_imgPath);
            AbstractC109779f.m84328b(jSONObject, "reserved", this.field_reserved);
            AbstractC109779f.m84328b(jSONObject, "lvbuffer", this.field_lvbuffer);
            AbstractC109779f.m84328b(jSONObject, "talkerId", Integer.valueOf(this.field_talkerId));
            AbstractC109779f.m84328b(jSONObject, "transContent", this.field_transContent);
            AbstractC109779f.m84328b(jSONObject, "transBrandWording", this.field_transBrandWording);
            AbstractC109779f.m84328b(jSONObject, "bizClientMsgId", this.field_bizClientMsgId);
            AbstractC109779f.m84328b(jSONObject, "bizChatId", Long.valueOf(this.field_bizChatId));
            AbstractC109779f.m84328b(jSONObject, "bizChatUserId", this.field_bizChatUserId);
            AbstractC109779f.m84328b(jSONObject, "msgSeq", Long.valueOf(this.field_msgSeq));
            AbstractC109779f.m84328b(jSONObject, "flag", Integer.valueOf(this.field_flag));
            AbstractC109779f.m84328b(jSONObject, "solitaireFoldInfo", this.field_solitaireFoldInfo);
            AbstractC109779f.m84328b(jSONObject, "fromUsername", this.field_fromUsername);
            AbstractC109779f.m84328b(jSONObject, "toUsername", this.field_toUsername);
            AbstractC109779f.m84328b(jSONObject, "historyId", this.field_historyId);
        } catch (Exception unused) {
        }
        return jSONObject;
    }

    /* renamed from: u0 */
    public String m94416u0() {
        return this.field_bizChatUserId;
    }

    /* renamed from: u1 */
    public void m94417u1(int i16) {
        this.f357071F = i16;
        this.f357103r = true;
    }

    /* renamed from: v0 */
    public String m94418v0() {
        return this.field_bizClientMsgId;
    }

    /* renamed from: w0 */
    public String m94419w0() {
        return this.f357081Q;
    }

    /* renamed from: w1 */
    public void m94420w1(long j16) {
        this.field_msgSeq = j16;
        this.f357111y = true;
    }

    /* renamed from: x0 */
    public int m94421x0() {
        return this.field_flag;
    }

    /* renamed from: x1 */
    public void m94422x1(long j16) {
        this.field_msgSvrId = j16;
        this.f357092e = true;
    }

    /* renamed from: y0 */
    public String mo54142y0() {
        return this.field_fromUsername;
    }

    /* renamed from: z0 */
    public String m94423z0() {
        return this.field_historyId;
    }

    @Override // aq4.AbstractC4001f0
    public void convertFrom(ContentValues contentValues, boolean z16) {
        if (contentValues.containsKey("msgId")) {
            this.field_msgId = contentValues.getAsLong("msgId").longValue();
            if (z16) {
                this.f357091d = true;
            }
        }
        if (contentValues.containsKey("msgSvrId")) {
            this.field_msgSvrId = contentValues.getAsLong("msgSvrId").longValue();
            if (z16) {
                this.f357092e = true;
            }
        }
        if (contentValues.containsKey("type")) {
            this.field_type = contentValues.getAsInteger("type").intValue();
            if (z16) {
                this.f357093f = true;
            }
        }
        if (contentValues.containsKey("status")) {
            this.field_status = contentValues.getAsInteger("status").intValue();
            if (z16) {
                this.f357094g = true;
            }
        }
        if (contentValues.containsKey("isSend")) {
            this.field_isSend = contentValues.getAsInteger("isSend").intValue();
            if (z16) {
                this.f357095h = true;
            }
        }
        if (contentValues.containsKey("isShowTimer")) {
            this.field_isShowTimer = contentValues.getAsInteger("isShowTimer").intValue();
            if (z16) {
                this.f357096i = true;
            }
        }
        if (contentValues.containsKey("createTime")) {
            this.field_createTime = contentValues.getAsLong("createTime").longValue();
            if (z16) {
                this.f357097m = true;
            }
        }
        if (contentValues.containsKey("talker")) {
            this.field_talker = contentValues.getAsString("talker");
            if (z16) {
                this.f357098n = true;
            }
        }
        if (contentValues.containsKey("content")) {
            this.field_content = contentValues.getAsString("content");
            if (z16) {
                this.f357099o = true;
            }
        }
        if (contentValues.containsKey("imgPath")) {
            this.field_imgPath = contentValues.getAsString("imgPath");
            if (z16) {
                this.f357100p = true;
            }
        }
        if (contentValues.containsKey("reserved")) {
            this.field_reserved = contentValues.getAsString("reserved");
            if (z16) {
                this.f357102q = true;
            }
        }
        if (contentValues.containsKey("lvbuffer")) {
            this.field_lvbuffer = contentValues.getAsByteArray("lvbuffer");
            if (z16) {
                this.f357103r = true;
            }
        }
        if (contentValues.containsKey("talkerId")) {
            this.field_talkerId = contentValues.getAsInteger("talkerId").intValue();
            if (z16) {
                this.f357104s = true;
            }
        }
        if (contentValues.containsKey("transContent")) {
            this.field_transContent = contentValues.getAsString("transContent");
            if (z16) {
                this.f357105t = true;
            }
        }
        if (contentValues.containsKey("transBrandWording")) {
            this.field_transBrandWording = contentValues.getAsString("transBrandWording");
            if (z16) {
                this.f357106u = true;
            }
        }
        if (contentValues.containsKey("bizClientMsgId")) {
            this.field_bizClientMsgId = contentValues.getAsString("bizClientMsgId");
            if (z16) {
                this.f357107v = true;
            }
        }
        if (contentValues.containsKey("bizChatId")) {
            this.field_bizChatId = contentValues.getAsLong("bizChatId").longValue();
            if (z16) {
                this.f357108w = true;
            }
        }
        if (contentValues.containsKey("bizChatUserId")) {
            this.field_bizChatUserId = contentValues.getAsString("bizChatUserId");
            if (z16) {
                this.f357109x = true;
            }
        }
        if (contentValues.containsKey("msgSeq")) {
            this.field_msgSeq = contentValues.getAsLong("msgSeq").longValue();
            if (z16) {
                this.f357111y = true;
            }
        }
        if (contentValues.containsKey("flag")) {
            this.field_flag = contentValues.getAsInteger("flag").intValue();
            if (z16) {
                this.f357112z = true;
            }
        }
        if (contentValues.containsKey("solitaireFoldInfo")) {
            try {
                byte[] asByteArray = contentValues.getAsByteArray("solitaireFoldInfo");
                if (asByteArray != null && asByteArray.length > 0) {
                    this.field_solitaireFoldInfo = (p04) new p04().parseFrom(asByteArray);
                    if (z16) {
                        this.f357066A = true;
                    }
                }
            } catch (IOException e16) {
                AbstractC59537n2.m53224e("MicroMsg.SDK.BaseMsgInfo", e16.getMessage(), null);
            }
        }
        if (contentValues.containsKey("fromUsername")) {
            this.field_fromUsername = contentValues.getAsString("fromUsername");
            if (z16) {
                this.f357067B = true;
            }
        }
        if (contentValues.containsKey("toUsername")) {
            this.field_toUsername = contentValues.getAsString("toUsername");
            if (z16) {
                this.f357068C = true;
            }
        }
        if (contentValues.containsKey("historyId")) {
            this.field_historyId = contentValues.getAsString("historyId");
            if (z16) {
                this.f357069D = true;
            }
        }
        if (contentValues.containsKey("rowid")) {
            this.systemRowid = contentValues.getAsLong("rowid").longValue();
        }
    }
}


package et0;

import kotlin.Metadata;
import kotlin.jvm.internal.AbstractC100682o;
import vp4.AbstractC149175f0;
import vp4.C149187r;
import yp4.AbstractC161758b;

@Metadata(m79731d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\u000f\u0012\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, m79732d2 = {"Let0/o;", "Lvp4/f0;", "Let0/e;", "Lyp4/b;", "Lvp4/r;", "pipeline", "<init>", "(Lvp4/r;)V", "plugin-messenger-foundation_release"}, m79733k = 1, m79734mv = {1, 9, 0})
/* renamed from: et0.o */
/* loaded from: classes9.dex */
public final class C77023o extends AbstractC149175f0<C77013e, AbstractC161758b> {
    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public C77023o(C149187r pipeline) {
        super(pipeline);
        AbstractC100682o.m79781g(pipeline, "pipeline");
    }

    /* JADX WARN: Removed duplicated region for block: B:36:0x0162  */
    /* JADX WARN: Removed duplicated region for block: B:59:0x01d6  */
    @Override // vp4.AbstractC149175f0
    /* renamed from: i */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.Object mo62223i(vp4.C149195z r20, yp4.AbstractC161758b r21, kotlin.coroutines.Continuation r22) {
        /*
            r19 = this;
            r0 = r21
            et0.e r0 = (et0.C77013e) r0
            vp4.z r1 = r19.m106206h()
            java.lang.String r2 = "PPCKey_Params"
            java.lang.Object r1 = r1.m106207d(r2)
            ct0.t1 r1 = (ct0.C67288t1) r1
            if (r1 != 0) goto L1d
            vp4.v r0 = new vp4.v
            r1 = r20
            r0.<init>(r1)
            r4 = r19
            goto L2e8
        L1d:
            com.tencent.mm.storage.f8 r0 = r0.f240321b
            r2 = 1
            r0.mo54074C1(r2)
            java.lang.String r3 = r1.f215694b
            r0.m94391J1(r3)
            vp4.z r3 = r19.m106206h()
            java.lang.String r4 = "PPCKey_InitCreateTime"
            java.lang.Object r3 = r3.m106207d(r4)
            java.lang.Long r3 = (java.lang.Long) r3
            if (r3 == 0) goto L3b
            long r3 = r3.longValue()
            goto L41
        L3b:
            java.lang.String r3 = r1.f215694b
            long r3 = pr0.AbstractC123169a9.m91865o(r3)
        L41:
            r0.m94408e1(r3)
            r0.m94412n1(r2)
            java.lang.String r3 = r1.f215696d
            r0.m94407d1(r3)
            int r3 = r1.f215697e
            r0.setType(r3)
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "msg content.size="
            r3.<init>(r4)
            java.lang.String r4 = r0.getContent()
            int r4 = r4.length()
            r3.append(r4)
            java.lang.String r4 = ", createTime="
            r3.append(r4)
            long r4 = r0.getCreateTime()
            r3.append(r4)
            java.lang.String r4 = " pipeline:"
            r3.append(r4)
            r4 = r19
            vp4.r r5 = r4.f427713a
            int r5 = r5.hashCode()
            r3.append(r5)
            java.lang.String r3 = r3.toString()
            java.lang.String r5 = "MicroMsg.SendMsgPPC.SendMsgFillCommonPPC"
            r6 = 0
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53229j(r5, r3, r6)
            java.lang.Class<p23.m3> r3 = p23.InterfaceC119893m3.class
            ur4.m r3 = ur4.AbstractC145208n0.m104254c(r3)
            p23.m3 r3 = (p23.InterfaceC119893m3) r3
            ct0.k1 r3 = (ct0.C67255k1) r3
            java.lang.String r3 = r3.m61371db(r0)
            if (r3 == 0) goto La8
            r0.m54122g3(r3)
            java.lang.String r3 = r0.f357072G
            java.lang.Object[] r3 = new java.lang.Object[]{r3}
            java.lang.String r7 = "defaultMsgSource:MsgSource:%s"
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53229j(r5, r7, r3)
        La8:
            pr0.o6 r3 = r1.f215703k
            long r7 = r3.f343744b
            r9 = 0
            int r3 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            r7 = 2
            r8 = 0
            if (r3 == 0) goto Lbb
            java.lang.String r3 = pr0.AbstractC123340m9.m92029F(r7)
            pr0.AbstractC123340m9.m92038O(r0, r3, r8)
        Lbb:
            int r3 = r1.f215698f
            int r9 = r1.f215697e
            java.lang.Object r10 = r1.f215700h
            java.lang.String r11 = r0.f357072G
            java.lang.String r12 = "getMsgSource(...)"
            kotlin.jvm.internal.AbstractC100682o.m79780f(r11, r12)
            boolean r12 = r1.f215705m
            java.lang.String r13 = "</msgsource>"
            java.lang.String r14 = "<msgsource>"
            if (r12 == 0) goto L124
            java.lang.String r12 = r1.f215706n
            boolean r12 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r12)
            if (r12 != 0) goto L124
            java.lang.String r12 = r1.f215706n
            java.lang.Object[] r12 = new java.lang.Object[]{r11, r12}
            java.lang.String r15 = "[mergeMsgSource] rawSource:%s weAppSourceUsername:%s"
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53229j(r5, r15, r12)
            java.lang.StringBuffer r12 = new java.lang.StringBuffer
            r12.<init>()
            boolean r15 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r11)
            if (r15 == 0) goto Lf1
            r12.append(r14)
        Lf1:
            java.lang.String r15 = "<weappsourceUsername>"
            r12.append(r15)
            java.lang.String r1 = r1.f215706n
            r12.append(r1)
            java.lang.String r1 = "</weappsourceUsername>"
            r12.append(r1)
            boolean r1 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r11)
            if (r1 == 0) goto L114
            r12.append(r13)
            java.lang.String r1 = r12.toString()
            java.lang.String r11 = "toString(...)"
            kotlin.jvm.internal.AbstractC100682o.m79780f(r1, r11)
            goto L125
        L114:
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>(r14)
            r1.append(r12)
            java.lang.String r1 = r1.toString()
            java.lang.String r11 = eg5.AbstractC75739c0.m66546s(r11, r14, r1, r8)
        L124:
            r1 = r11
        L125:
            r0.m54122g3(r1)
            java.lang.String r1 = r0.f357072G
            r11 = 3
            java.lang.Object[] r12 = new java.lang.Object[r11]
            r12[r8] = r1
            if (r10 != 0) goto L133
            r15 = r2
            goto L134
        L133:
            r15 = r8
        L134:
            java.lang.Boolean r15 = java.lang.Boolean.valueOf(r15)
            r12[r2] = r15
            java.lang.Integer r15 = java.lang.Integer.valueOf(r3)
            r12[r7] = r15
            java.lang.String r15 = "[mergeMsgSource] rawSource:%s args is null:%s flag:%s"
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53229j(r5, r15, r12)
            boolean r12 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r1)
            java.lang.String r15 = "similar_paste_seq"
            if (r12 != 0) goto L162
            kotlin.jvm.internal.AbstractC100682o.m79778d(r1)
            boolean r12 = eg5.AbstractC75739c0.m66551x(r1, r14, r8)
            if (r12 != 0) goto L162
            java.lang.String r12 = "[mergeMsgSource] WTF the msgsource is right? %s"
            java.lang.Object[] r13 = new java.lang.Object[]{r1}
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53236q(r5, r12, r13)
            goto L207
        L162:
            r12 = r3 & 1
            if (r12 == 0) goto L207
            boolean r12 = r10 instanceof java.util.HashMap
            if (r12 == 0) goto L207
            java.lang.StringBuffer r12 = new java.lang.StringBuffer
            r12.<init>()
            boolean r16 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r1)
            if (r16 == 0) goto L178
            r12.append(r14)
        L178:
            r16 = r10
            java.util.Map r16 = (java.util.Map) r16
            java.util.Set r16 = r16.entrySet()
            java.util.Iterator r16 = r16.iterator()
        L184:
            boolean r17 = r16.hasNext()
            if (r17 == 0) goto L1e4
            java.lang.Object r17 = r16.next()
            java.util.Map$Entry r17 = (java.util.Map.Entry) r17
            java.lang.Object r6 = r17.getKey()
            java.lang.Object r7 = r17.getValue()
            boolean r11 = r6 instanceof java.lang.String
            if (r11 == 0) goto L1d6
            boolean r11 = r7 instanceof java.lang.String
            if (r11 == 0) goto L1d6
            r11 = r7
            java.lang.String r11 = (java.lang.String) r11
            boolean r17 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r11)
            if (r17 != 0) goto L1d6
            r8 = r6
            java.lang.String r8 = (java.lang.String) r8
            boolean r18 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r8)
            if (r18 == 0) goto L1b3
            goto L1d6
        L1b3:
            boolean r6 = eg5.AbstractC75739c0.m66541n(r15, r8, r2)
            if (r6 == 0) goto L1ba
            goto L1df
        L1ba:
            java.lang.String r6 = "<"
            r12.append(r6)
            r12.append(r8)
            java.lang.String r6 = ">"
            r12.append(r6)
            r12.append(r11)
            java.lang.String r7 = "</"
            r12.append(r7)
            r12.append(r8)
            r12.append(r6)
            goto L1df
        L1d6:
            java.lang.String r8 = "%s %s"
            java.lang.Object[] r6 = new java.lang.Object[]{r6, r7}
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53236q(r5, r8, r6)
        L1df:
            r6 = 0
            r7 = 2
            r8 = 0
            r11 = 3
            goto L184
        L1e4:
            boolean r6 = com.tencent.p087mm.sdk.platformtools.AbstractC59543n8.m53271J0(r1)
            if (r6 == 0) goto L1f2
            r12.append(r13)
            java.lang.String r1 = r12.toString()
            goto L207
        L1f2:
            if (r1 == 0) goto L206
            java.lang.StringBuilder r6 = new java.lang.StringBuilder
            r6.<init>(r14)
            r6.append(r12)
            java.lang.String r6 = r6.toString()
            r7 = 0
            java.lang.String r1 = eg5.AbstractC75739c0.m66546s(r1, r14, r6, r7)
            goto L207
        L206:
            r1 = 0
        L207:
            r0.m54122g3(r1)
            boolean r1 = r0.m54090Q2()
            if (r1 == 0) goto L231
            int r1 = pr0.AbstractC123340m9.f343703a
            pd5.d r1 = pd5.AbstractC121435e.f340697d
            r6 = 2147483647(0x7fffffff, double:1.060997895E-314)
            long r6 = r1.mo91148g(r6)
            r11 = 1
            long r6 = r6 + r11
            java.lang.String r1 = r0.f357072G
            java.lang.String r6 = java.lang.String.valueOf(r6)
            java.lang.String r7 = "eggSeed"
            java.lang.String r6 = sp4.AbstractC137580b.m99718i(r6, r7)
            java.lang.String r1 = pr0.AbstractC123340m9.m92043b(r1, r7, r6)
            r0.m54122g3(r1)
        L231:
            if (r3 != r2) goto L240
            r1 = 42
            if (r9 != r1) goto L240
            int r1 = r0.f357071F
            r1 = r1 | 512(0x200, float:7.17E-43)
            r0.m94417u1(r1)
            goto L2e2
        L240:
            r1 = r3 & 4
            java.lang.String r2 = ".msgsource.alnode.inlenlist"
            java.lang.String r6 = ".msgsource.alnode.cf"
            if (r1 != 0) goto L29c
            r7 = r3 & 8
            if (r7 == 0) goto L24d
            goto L29c
        L24d:
            r1 = r3 & 16
            if (r1 != 0) goto L255
            r3 = r3 & 32
            if (r3 == 0) goto L2e2
        L255:
            if (r1 == 0) goto L259
            r1 = 4
            goto L25a
        L259:
            r1 = 5
        L25a:
            java.lang.Integer r3 = new java.lang.Integer
            r3.<init>(r1)
            java.lang.Object[] r3 = new java.lang.Object[]{r3}
            java.lang.String r7 = "has paste similar change flag, %d"
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53229j(r5, r7, r3)
            java.util.HashMap r3 = new java.util.HashMap
            r3.<init>()
            java.lang.String r1 = java.lang.String.valueOf(r1)
            r3.put(r6, r1)
            boolean r1 = kotlin.jvm.internal.AbstractC100680m0.m79772h(r10)
            if (r1 == 0) goto L27d
            java.util.Map r10 = (java.util.Map) r10
            goto L27e
        L27d:
            r10 = 0
        L27e:
            if (r10 == 0) goto L293
            java.lang.Object r1 = r10.get(r15)
            boolean r5 = r1 instanceof java.lang.String
            if (r5 == 0) goto L28c
            r6 = r1
            java.lang.String r6 = (java.lang.String) r6
            goto L28d
        L28c:
            r6 = 0
        L28d:
            if (r6 != 0) goto L290
            goto L293
        L290:
            r3.put(r2, r6)
        L293:
            java.lang.String r1 = pr0.AbstractC123340m9.m92030G(r3)
            r2 = 0
            pr0.AbstractC123340m9.m92038O(r0, r1, r2)
            goto L2e2
        L29c:
            if (r1 == 0) goto L2a0
            r7 = 2
            goto L2a1
        L2a0:
            r7 = 3
        L2a1:
            java.lang.Integer r1 = new java.lang.Integer
            r1.<init>(r7)
            java.lang.Object[] r1 = new java.lang.Object[]{r1}
            java.lang.String r3 = "has paste fully flag, %d"
            com.tencent.p087mm.sdk.platformtools.AbstractC59537n2.m53229j(r5, r3, r1)
            java.util.HashMap r1 = new java.util.HashMap
            r1.<init>()
            java.lang.String r3 = java.lang.String.valueOf(r7)
            r1.put(r6, r3)
            boolean r3 = kotlin.jvm.internal.AbstractC100680m0.m79772h(r10)
            if (r3 == 0) goto L2c4
            java.util.Map r10 = (java.util.Map) r10
            goto L2c5
        L2c4:
            r10 = 0
        L2c5:
            if (r10 == 0) goto L2da
            java.lang.Object r3 = r10.get(r15)
            boolean r5 = r3 instanceof java.lang.String
            if (r5 == 0) goto L2d3
            r6 = r3
            java.lang.String r6 = (java.lang.String) r6
            goto L2d4
        L2d3:
            r6 = 0
        L2d4:
            if (r6 != 0) goto L2d7
            goto L2da
        L2d7:
            r1.put(r2, r6)
        L2da:
            java.lang.String r1 = pr0.AbstractC123340m9.m92030G(r1)
            r2 = 0
            pr0.AbstractC123340m9.m92038O(r0, r1, r2)
        L2e2:
            et0.f r1 = new et0.f
            r1.<init>(r0)
            r0 = r1
        L2e8:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: et0.C77023o.mo62223i(vp4.z, yp4.b, kotlin.coroutines.Continuation):java.lang.Object");
    }
}