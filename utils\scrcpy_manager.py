#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scrcpy管理器
集成scrcpy实现高性能屏幕镜像
"""

import os
import sys
import subprocess
import threading
import time
import glob
from typing import Optional, Dict, List
import socket
from PyQt5.QtCore import QObject, pyqtSignal, QProcess, QSize
from PyQt5.QtWidgets import QWidget
import re

class ScrcpyManager(QObject):
    """Scrcpy管理器，用于集成scrcpy实现高性能屏幕镜像"""
    
    # 定义信号
    process_started = pyqtSignal(str)  # 进程启动信号
    process_error = pyqtSignal(str, str)  # 进程错误信号
    process_finished = pyqtSignal(str, int)  # 进程结束信号
    
    def __init__(self):
        super().__init__()
        # 工具目录
        self.tools_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "tools")
        # scrcpy可执行文件
        self.scrcpy_exe = self._find_scrcpy_exe()
        self.processes: Dict[str, QProcess] = {}
        self.window_titles: Dict[str, str] = {}
    
    def _find_scrcpy_exe(self) -> Optional[str]:
        """查找scrcpy可执行文件，支持带版本号的目录"""
        # 尝试查找所有可能的scrcpy目录
        scrcpy_dirs = glob.glob(os.path.join(self.tools_dir, "scrcpy-win64*"))
        
        if not scrcpy_dirs:
            print("未找到scrcpy目录")
            return None
        
        # 按照名称排序，选择最新版本
        scrcpy_dirs.sort(reverse=True)
        scrcpy_dir = scrcpy_dirs[0]
        print(f"找到scrcpy目录: {scrcpy_dir}")
        
        # 检查scrcpy.exe是否存在
        scrcpy_exe = os.path.join(scrcpy_dir, "scrcpy.exe")
        if os.path.exists(scrcpy_exe):
            print(f"找到scrcpy.exe: {scrcpy_exe}")
            return scrcpy_exe
        
        print(f"在目录 {scrcpy_dir} 中未找到scrcpy.exe")
        return None
    
    def is_scrcpy_available(self) -> bool:
        """检查scrcpy是否可用"""
        return self.scrcpy_exe is not None and os.path.exists(self.scrcpy_exe)
    
    def download_scrcpy(self, callback=None):
        """下载scrcpy"""
        import requests
        import zipfile
        import io
        
        try:
            # 创建tools目录
            os.makedirs(self.tools_dir, exist_ok=True)
            
            # 下载scrcpy
            url = "https://github.com/Genymobile/scrcpy/releases/download/v2.1.1/scrcpy-win64-v2.1.1.zip"
            if callback:
                callback("正在下载scrcpy...")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            # 解压缩
            if callback:
                callback("正在解压scrcpy...")
            
            with zipfile.ZipFile(io.BytesIO(response.content)) as zip_ref:
                zip_ref.extractall(self.tools_dir)
            
            # 重新查找scrcpy可执行文件
            self.scrcpy_exe = self._find_scrcpy_exe()
            
            if callback:
                if self.scrcpy_exe:
                    callback(f"scrcpy下载完成: {self.scrcpy_exe}")
                else:
                    callback("scrcpy下载完成，但未找到可执行文件")
            
            return self.is_scrcpy_available()
        except Exception as e:
            if callback:
                callback(f"下载scrcpy失败: {e}")
            return False
    
    def start_scrcpy(self, serial: str, window_title: str = None, 
                    bitrate: int = 8000000, max_fps: int = 60,
                    max_size: int = 0, always_on_top: bool = True,
                    window_x: int = 100, window_y: int = 100,
                    window_width: int = None, window_height: int = None,
                    no_control: bool = False) -> bool:
        """启动scrcpy进程"""
        try:
            # 检查scrcpy是否已经启动
            if serial in self.processes:
                return True
            
            # 检查scrcpy.exe是否存在
            if not self.is_scrcpy_available():
                print("scrcpy.exe不存在")
                return False
            
            # 设置窗口标题
            if not window_title:
                window_title = f"scrcpy-{serial}"
            
            self.window_titles[serial] = window_title
            
            # 获取设备屏幕尺寸并计算合适的窗口大小
            device_size = self._get_device_screen_size(serial)
            if device_size:
                calculated_width, calculated_height = self._calculate_window_size(device_size)
                if window_width is None:
                    window_width = calculated_width
                if window_height is None:
                    window_height = calculated_height
            
            # 确保窗口大小有默认值
            if window_width is None:
                window_width = 450
            if window_height is None:
                window_height = 800
            
            # 构建命令行参数
            args = [
                "-s", serial,
                "--window-title", window_title,
                "--video-bit-rate", str(bitrate),
                "--max-fps", str(max_fps),
                "--window-x", str(window_x),
                "--window-y", str(window_y),
                "--window-width", str(window_width),
                "--window-height", str(window_height)
            ]
            
            if max_size > 0:
                args.extend(["--max-size", str(max_size)])
            
            if always_on_top:
                args.append("--always-on-top")
            
            if no_control:
                args.append("--no-control")
            
            # 设置工作目录为scrcpy所在目录，确保可以找到相关DLL文件
            scrcpy_dir = os.path.dirname(self.scrcpy_exe)
            
            # 创建QProcess
            process = QProcess()
            process.setProgram(self.scrcpy_exe)
            process.setArguments(args)
            process.setWorkingDirectory(scrcpy_dir)
            
            # 输出错误和标准输出
            def handle_stdout():
                data = process.readAllStandardOutput().data().decode('utf-8', errors='replace')
                if data:
                    print(f"scrcpy输出: {data}")
                    
            def handle_stderr():
                data = process.readAllStandardError().data().decode('utf-8', errors='replace')
                if data:
                    print(f"scrcpy错误: {data}")
                    
            process.readyReadStandardOutput.connect(handle_stdout)
            process.readyReadStandardError.connect(handle_stderr)
            
            # 连接信号
            process.started.connect(lambda: self.process_started.emit(serial))
            process.errorOccurred.connect(lambda error: self.process_error.emit(serial, str(error)))
            process.finished.connect(lambda code, status: self.process_finished.emit(serial, code))
            
            # 启动进程
            process.start()
            
            # 检查是否成功启动
            if not process.waitForStarted(5000):  # 等待5秒
                print(f"scrcpy进程启动失败: {process.errorString()}")
                return False
            
            # 保存进程
            self.processes[serial] = process
            
            # 打印启动信息
            print(f"scrcpy进程已启动，PID: {process.processId()}")
            print(f"scrcpy命令: {self.scrcpy_exe} {' '.join(args)}")
            print(f"设备屏幕尺寸: {device_size}, 窗口尺寸: {window_width}x{window_height}")
            
            return True
        except Exception as e:
            print(f"启动scrcpy失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _get_device_screen_size(self, serial: str) -> tuple:
        """获取设备屏幕尺寸"""
        try:
            # 使用adb命令获取屏幕尺寸
            cmd = ["adb", "-s", serial, "shell", "wm", "size"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            output = result.stdout.strip()
            
            # 解析输出，格式类似于 "Physical size: 1080x2340"
            match = re.search(r"(\d+)x(\d+)", output)
            if match:
                width = int(match.group(1))
                height = int(match.group(2))
                return (width, height)
            
            return None
        except Exception as e:
            print(f"获取设备屏幕尺寸失败: {e}")
            return None
    
    def _calculate_window_size(self, device_size: tuple) -> tuple:
        """根据设备屏幕尺寸计算合适的窗口大小"""
        if not device_size:
            return (450, 800)  # 默认尺寸
        
        width, height = device_size
        
        # 确保纵横比保持不变
        aspect_ratio = width / height
        
        # 如果设备是横屏（宽度大于高度）
        if width > height:
            # 限制宽度最大为800
            new_width = min(800, width)
            new_height = int(new_width / aspect_ratio)
        else:  # 竖屏
            # 限制高度最大为800
            new_height = min(800, height)
            new_width = int(new_height * aspect_ratio)
        
        # 确保尺寸合理
        new_width = max(300, new_width)
        new_height = max(400, new_height)
        
        return (new_width, new_height)
    
    def stop_scrcpy(self, serial: str) -> bool:
        """停止scrcpy进程"""
        try:
            if serial in self.processes:
                process = self.processes[serial]
                
                # 终止进程
                process.terminate()
                if not process.waitForFinished(3000):  # 等待3秒
                    process.kill()
                
                # 移除进程
                del self.processes[serial]
                
                if serial in self.window_titles:
                    del self.window_titles[serial]
                
                return True
            return False
        except Exception as e:
            print(f"停止scrcpy失败: {e}")
            return False
    
    def is_running(self, serial: str) -> bool:
        """检查scrcpy是否正在运行"""
        return serial in self.processes and self.processes[serial].state() == QProcess.Running
    
    def get_window_title(self, serial: str) -> Optional[str]:
        """获取scrcpy窗口标题"""
        return self.window_titles.get(serial)
    
    def cleanup(self):
        """清理资源"""
        for serial in list(self.processes.keys()):
            self.stop_scrcpy(serial) 