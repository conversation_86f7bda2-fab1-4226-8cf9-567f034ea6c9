/**
 * 微信主动发送消息API
 * 基于Hook分析的微信内部机制实现
 * 使用方法: SendMessage(wxid, content)
 */

console.log("🚀 微信主动发送消息API加载中...");

Java.perform(function() {
    
    // 工具函数
    function getMessageTypeDesc(type) {
        var types = {
            1: "文本消息", 3: "图片消息", 34: "语音消息",
            43: "视频消息", 47: "表情消息", 48: "位置消息",
            49: "链接消息", 42: "名片消息", 10000: "系统消息"
        };
        return types[type] || `未知类型(${type})`;
    }
    
    /**
     * 主动发送微信消息 - 完整版
     * @param {string} wxid - 接收人微信ID (如: wxid_xxx 或群ID)
     * @param {string} content - 消息内容
     * @param {number} type - 消息类型 (默认1=文本消息)
     * @returns {boolean} 发送是否成功
     */
    function SendMessage(wxid, content, type) {
        type = type || 1; // 默认文本消息
        
        if (!wxid || !content) {
            console.log("[-] 参数错误: wxid和content不能为空");
            return false;
        }
        
        try {
            console.log("\n" + "=".repeat(60));
            console.log("🚀 [主动发送] SendMessage API调用");
            console.log("=".repeat(60));
            console.log(`[+] 接收人: ${wxid}`);
            console.log(`[+] 内容: ${content}`);
            console.log(`[+] 类型: ${type} (${getMessageTypeDesc(type)})`);
            console.log(`[+] 时间: ${new Date().toLocaleString('zh-CN')}`);
            
            // 获取必要的类
            var MessageClass = Java.use("qk.t7");
            var SenderClass = Java.use("et0.o");
            
            console.log("[+] 步骤1: 获取核心类完成");
            
            // 方法1: 尝试通过现有实例发送
            try {
                // 创建消息对象
                var messageObj = MessageClass.$new();
                
                // 设置消息属性 (按照Hook观察到的顺序)
                var currentTime = Date.now();
                
                messageObj.C1(1);                    // 设置状态为1
                messageObj.J1(wxid);                 // 设置接收人
                messageObj.e1(currentTime);          // 设置创建时间
                messageObj.n1(1);                    // 设置发送标识为1
                messageObj.d1(content);              // 设置消息内容
                messageObj.setType(type);            // 设置消息类型
                
                console.log("[+] 步骤2: 消息对象属性设置完成");
                
                // 尝试获取现有的发送器实例
                var senderInstance = null;
                
                // 方法: 通过反射查找现有实例
                Java.choose("et0.o", {
                    onMatch: function(instance) {
                        senderInstance = instance;
                        console.log("[+] 找到现有发送器实例");
                        return "stop"; // 找到第一个就停止
                    },
                    onComplete: function() {
                        if (!senderInstance) {
                            console.log("[-] 未找到现有发送器实例");
                        }
                    }
                });
                
                if (senderInstance) {
                    // 使用现有实例发送
                    console.log("[+] 步骤3: 使用现有发送器实例");
                    
                    // 创建简单的配置对象
                    var ConfigClass = Java.use("et0.e");
                    var configObj = ConfigClass.$new();
                    configObj.b = messageObj;
                    
                    // 创建消息容器
                    var ContainerClass = Java.use("vp4.z");
                    var containerObj = ContainerClass.$new(messageObj);
                    
                    console.log("[+] 步骤4: 配置和容器对象创建完成");
                    
                    // 创建简单的协程回调
                    var ContinuationImpl = Java.registerClass({
                        name: 'com.frida.ContinuationImpl',
                        implements: [Java.use('kotlin.coroutines.Continuation')],
                        methods: {
                            resumeWith: function(result) {
                                console.log("[+] 协程回调: 消息发送完成");
                                console.log(`[+] 结果: ${result}`);
                            },
                            getContext: function() {
                                return Java.use('kotlin.coroutines.EmptyCoroutineContext').INSTANCE;
                            }
                        }
                    });
                    
                    var continuation = ContinuationImpl.$new();
                    console.log("[+] 步骤5: 协程回调创建完成");
                    
                    // 调用核心发送方法
                    console.log("[*] 步骤6: 调用核心发送方法...");
                    var result = senderInstance.i(containerObj, configObj, continuation);
                    
                    console.log("[+] 步骤7: 发送方法调用完成");
                    console.log(`[+] 返回结果: ${result}`);
                    console.log("=".repeat(60));
                    
                    return true;
                }
                
            } catch (e) {
                console.log(`[-] 方法1失败: ${e}`);
            }
            
            // 方法2: 尝试模拟UI发送流程
            try {
                console.log("[*] 尝试方法2: 模拟UI发送流程");
                
                // 查找ChatFooter实例
                var chatFooterInstance = null;
                Java.choose("com.tencent.mm.pluginsdk.ui.chat.ChatFooter", {
                    onMatch: function(instance) {
                        chatFooterInstance = instance;
                        console.log("[+] 找到ChatFooter实例");
                        return "stop";
                    },
                    onComplete: function() {}
                });
                
                if (chatFooterInstance) {
                    // 尝试设置输入框内容并触发发送
                    // 这需要更深入的UI操作，暂时跳过
                    console.log("[*] ChatFooter实例可用，但需要更复杂的UI操作");
                }
                
            } catch (e) {
                console.log(`[-] 方法2失败: ${e}`);
            }
            
            console.log("[-] 所有发送方法都失败了");
            return false;
            
        } catch (e) {
            console.log(`[-] SendMessage发生严重错误: ${e}`);
            console.log(`[-] 错误堆栈: ${e.stack}`);
            return false;
        }
    }
    
    /**
     * 简化版发送函数
     * @param {string} wxid - 接收人微信ID
     * @param {string} content - 消息内容
     * @returns {boolean} 发送是否成功
     */
    function SendMsg(wxid, content) {
        return SendMessage(wxid, content, 1);
    }
    
    /**
     * 获取当前聊天对象的wxid
     * @returns {string} 当前聊天对象的wxid
     */
    function getCurrentChatWxid() {
        try {
            // 这里需要根据具体的微信版本来实现
            // 暂时返回示例ID
            return "wxid_example";
        } catch (e) {
            console.log(`[-] 获取当前聊天wxid失败: ${e}`);
            return null;
        }
    }
    
    /**
     * 发送消息到当前聊天
     * @param {string} content - 消息内容
     * @returns {boolean} 发送是否成功
     */
    function SendToCurrentChat(content) {
        var wxid = getCurrentChatWxid();
        if (wxid) {
            return SendMessage(wxid, content, 1);
        } else {
            console.log("[-] 无法获取当前聊天对象");
            return false;
        }
    }
    
    // 将函数暴露到全局作用域
    global.SendMessage = SendMessage;
    global.SendMsg = SendMsg;
    global.SendToCurrentChat = SendToCurrentChat;
    global.getCurrentChatWxid = getCurrentChatWxid;
    
    console.log("\n" + "=".repeat(60));
    console.log("🎉 微信主动发送消息API加载完成！");
    console.log("=".repeat(60));
    console.log("📝 可用函数:");
    console.log("   SendMessage(wxid, content, type) - 完整发送函数");
    console.log("   SendMsg(wxid, content)           - 简化发送函数");
    console.log("   SendToCurrentChat(content)       - 发送到当前聊天");
    console.log("   getCurrentChatWxid()             - 获取当前聊天ID");
    console.log("=".repeat(60));
    console.log("💬 使用示例:");
    console.log("   SendMessage('wxid_xxx', 'Hello World', 1)");
    console.log("   SendMsg('wxid_xxx', 'Hello')");
    console.log("   SendToCurrentChat('Hello from Frida!')");
    console.log("=".repeat(60));
    
});
