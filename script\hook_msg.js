/**
 * 微信消息发送Hook脚本
 * 基于JADX反编译代码分析的完整消息发送流程监控
 * 使用原始类名，支持完整的消息发送过程Hook
 */

console.log("🚀 微信消息发送Hook脚本启动");

// 工具函数
function formatTimestamp(timestamp) {
    var date = new Date(timestamp);
    return date.toLocaleString('zh-CN');
}

function getMessageTypeDesc(type) {
    var types = {
        1: "文本消息", 3: "图片消息", 34: "语音消息",
        43: "视频消息", 47: "表情消息", 48: "位置消息",
        49: "链接消息", 42: "名片消息", 10000: "系统消息"
    };
    return types[type] || `未知类型(${type})`;
}

function printStackTrace(tag) {
    console.log(`[${tag}] 调用堆栈:`);
    var stack = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new());
    console.log(stack);
}

Java.perform(function() {
    console.log("[+] Java环境初始化完成");
    
    var messageStats = {
        totalMessages: 0,
        textMessages: 0,
        imageMessages: 0,
        voiceMessages: 0,
        videoMessages: 0,
        otherMessages: 0
    };
    
    try {
        // ==================== 消息对象Hook (qk.t7) ====================
        console.log("\n[*] Hook消息对象类 qk.t7...");
        
        var MessageClass = Java.use("qk.t7");
        console.log("[+] 成功找到消息类: qk.t7");
        
        // Hook 消息内容设置
        MessageClass.d1.implementation = function(content) {
            console.log("\n" + "=".repeat(50));
            console.log("[消息内容设置] d1 被调用");
            console.log(`[+] 消息内容: ${content}`);
            console.log(`[+] 内容长度: ${content ? content.length : 0} 字符`);
            
            var result = this.d1(content);
            console.log("[+] 消息内容设置完成");
            return result;
        };
        
        // Hook 接收人设置
        MessageClass.J1.implementation = function(talker) {
            console.log("\n" + "=".repeat(50));
            console.log("[接收人设置] J1 被调用");
            console.log(`[+] 接收人ID: ${talker}`);
            
            var result = this.J1(talker);
            console.log("[+] 接收人设置完成");
            return result;
        };
        
        // Hook 创建时间设置
        MessageClass.e1.implementation = function(timestamp) {
            console.log("\n" + "=".repeat(50));
            console.log("[创建时间设置] e1 被调用");
            console.log(`[+] 时间戳: ${timestamp}`);
            console.log(`[+] 格式化时间: ${formatTimestamp(timestamp)}`);
            
            var result = this.e1(timestamp);
            console.log("[+] 创建时间设置完成");
            return result;
        };
        
        // Hook 消息类型设置
        MessageClass.setType.implementation = function(type) {
            console.log("\n" + "=".repeat(50));
            console.log("[消息类型设置] setType 被调用");
            console.log(`[+] 消息类型: ${type} (${getMessageTypeDesc(type)})`);
            
            var result = this.setType(type);
            
            // 更新统计
            messageStats.totalMessages++;
            switch (type) {
                case 1: messageStats.textMessages++; break;
                case 3: messageStats.imageMessages++; break;
                case 34: messageStats.voiceMessages++; break;
                case 43: messageStats.videoMessages++; break;
                default: messageStats.otherMessages++; break;
            }
            
            console.log("[+] 消息类型设置完成");
            return result;
        };
        
        // Hook 发送标识设置
        MessageClass.n1.implementation = function(isSend) {
            console.log("\n" + "=".repeat(50));
            console.log("[发送标识设置] n1 被调用");
            console.log(`[+] 是否发送: ${isSend} (${isSend === 1 ? '发送' : '接收'})`);
            
            var result = this.n1(isSend);
            console.log("[+] 发送标识设置完成");
            return result;
        };
        
        // Hook 消息状态设置 (基于反编译代码)
        try {
            MessageClass.C1.implementation = function(status) {
                console.log("\n" + "=".repeat(50));
                console.log("[消息状态设置] C1 被调用");
                console.log(`[+] 消息状态: ${status}`);
                
                var result = this.C1(status);
                console.log("[+] 消息状态设置完成");
                return result;
            };
        } catch (e) {
            console.log(`[-] Hook消息状态C1失败: ${e}`);
        }
        
        // Hook 消息源设置 (基于反编译代码)
        try {
            MessageClass.g3.implementation = function(msgSource) {
                console.log("\n" + "=".repeat(50));
                console.log("[消息源设置] g3 被调用");
                console.log(`[+] 消息源: ${msgSource}`);
                
                var result = this.g3(msgSource);
                console.log("[+] 消息源设置完成");
                return result;
            };
        } catch (e) {
            console.log(`[-] Hook消息源g3失败: ${e}`);
        }
        
        console.log("[+] 消息对象Hook设置完成");
        
    } catch (e) {
        console.log(`[-] Hook消息对象失败: ${e}`);
    }
    
    try {
        // ==================== 核心发送方法Hook (et0.o) ====================
        console.log("\n[*] Hook消息发送核心类 et0.o...");
        
        var SenderClass = Java.use("et0.o");
        console.log("[+] 成功找到发送类: et0.o");
        
        // Hook 核心发送方法 i
        SenderClass.i.implementation = function(messageContainer, config, continuation) {
            console.log("\n" + "=".repeat(60));
            console.log("🚀 [核心发送方法] et0.o.i 被调用");
            console.log("=".repeat(60));
            console.log(`[+] 消息容器(vp4.z): ${messageContainer}`);
            console.log(`[+] 配置对象(et0.e): ${config}`);
            console.log(`[+] 协程回调: ${continuation}`);
            
            // 基于反编译代码分析，尝试获取消息详情
            try {
                if (config && config.b) {
                    var msgObj = config.b;  // et0.e.b 是 f8 消息对象
                    console.log(`[+] 消息对象(f8): ${msgObj}`);
                    
                    // 获取消息详情
                    try {
                        var content = msgObj.getContent();
                        var talker = msgObj.getTalker();
                        var type = msgObj.getType();
                        var createTime = msgObj.getCreateTime();
                        var msgId = msgObj.getMsgId();
                        
                        console.log(`[+] 📝 完整消息详情:`);
                        console.log(`    消息ID: ${msgId}`);
                        console.log(`    内容: ${content}`);
                        console.log(`    接收人: ${talker}`);
                        console.log(`    类型: ${type} (${getMessageTypeDesc(type)})`);
                        console.log(`    时间: ${formatTimestamp(createTime)}`);
                    } catch (e) {
                        console.log(`[-] 获取消息详情失败: ${e}`);
                    }
                }
            } catch (e) {
                console.log(`[-] 获取配置对象信息失败: ${e}`);
            }
            
            // 打印调用堆栈
            printStackTrace("核心发送方法");
            
            console.log("[*] 准备调用原始发送方法...");
            var result = this.i(messageContainer, config, continuation);
            console.log("[+] 原始发送方法调用完成");
            console.log(`[+] 返回结果类型: ${result ? result.getClass().getName() : 'null'}`);
            
            return result;
        };
        
        console.log("[+] 消息发送核心Hook设置完成");
        
    } catch (e) {
        console.log(`[-] Hook消息发送核心失败: ${e}`);
    }
    
    try {
        // ==================== UI交互Hook ====================
        console.log("\n[*] Hook UI交互组件...");
        
        var ChatFooterClass = Java.use("com.tencent.mm.pluginsdk.ui.chat.ChatFooter");
        console.log("[+] 成功找到ChatFooter类");
        
        ChatFooterClass.A.implementation = function() {
            console.log("\n" + "=".repeat(50));
            console.log("🎯 [UI交互] ChatFooter.A 被调用 (发送按钮点击)");
            console.log("=".repeat(50));
            
            printStackTrace("UI发送按钮点击");
            
            var result = this.A.apply(this, arguments);
            console.log("[+] 发送按钮处理完成");
            return result;
        };
        
        console.log("[+] UI交互Hook设置完成");
        
    } catch (e) {
        console.log(`[-] Hook UI交互失败: ${e}`);
    }
    
    // 定期统计
    setInterval(function() {
        if (messageStats.totalMessages > 0) {
            console.log("\n" + "=".repeat(40));
            console.log("📊 消息发送统计信息");
            console.log("=".repeat(40));
            console.log(`总消息数: ${messageStats.totalMessages}`);
            console.log(`文本消息: ${messageStats.textMessages}`);
            console.log(`图片消息: ${messageStats.imageMessages}`);
            console.log(`语音消息: ${messageStats.voiceMessages}`);
            console.log(`视频消息: ${messageStats.videoMessages}`);
            console.log(`其他消息: ${messageStats.otherMessages}`);
            console.log("=".repeat(40));
        }
    }, 60000);
    
    console.log("\n" + "=".repeat(60));
    console.log("🎉 微信消息发送Hook脚本初始化完成！");
    console.log("=".repeat(60));
    console.log("📝 Hook覆盖范围:");
    console.log("   ✅ 消息对象完整属性设置 (qk.t7)");
    console.log("   ✅ 消息状态和源设置 (C1, g3)");
    console.log("   ✅ 核心发送方法增强监控 (et0.o.i)");
    console.log("   ✅ UI交互事件 (ChatFooter.A)");
    console.log("   ✅ 消息统计和监控");
    console.log("=".repeat(60));
    console.log("🔍 现在可以发送微信消息来查看完整的调用流程！");
    console.log("=".repeat(60));
    
});
