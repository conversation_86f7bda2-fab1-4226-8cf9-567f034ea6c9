/**
 * 微信消息发送完整Hook脚本
 * 基于JADX反编译代码分析的精确Hook点
 * 作者: AI Assistant
 * 日期: 2025-07-16
 */

console.log("=".repeat(60));
console.log("微信消息发送Hook脚本启动");
console.log("=".repeat(60));

// 工具函数：打印调用堆栈
function printStackTrace(tag) {
    console.log(`[${tag}] 调用堆栈:`);
    var stack = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new());
    console.log(stack);
}

// 工具函数：格式化时间戳
function formatTimestamp(timestamp) {
    var date = new Date(timestamp);
    return date.toLocaleString('zh-CN');
}

// 工具函数：获取消息类型描述
function getMessageTypeDesc(type) {
    var types = {
        1: "文本消息",
        3: "图片消息",
        34: "语音消息",
        43: "视频消息",
        47: "表情消息",
        48: "位置消息",
        49: "链接消息",
        10000: "系统消息"
    };
    return types[type] || `未知类型(${type})`;
}

Java.perform(function() {
    console.log("[+] Java环境初始化完成");

    try {
        // ==================== 第一部分：消息对象Hook ====================
        console.log("\n[*] 开始Hook消息对象类 qk.t7...");

        var MessageClass = Java.use("qk.t7");
        console.log("[+] 成功找到消息类: qk.t7");

        // Hook 1: 设置消息内容 (d1方法)
        MessageClass.d1.implementation = function(content) {
            console.log("\n" + "=".repeat(50));
            console.log("[消息内容设置] m94407d1 被调用");
            console.log(`[+] 消息内容: ${content}`);
            console.log(`[+] 内容长度: ${content ? content.length : 0} 字符`);
            console.log(`[+] 消息对象: ${this}`);

            // 打印调用堆栈
            printStackTrace("消息内容设置");

            // 调用原方法
            var result = this.m94407d1(content);
            console.log("[+] 消息内容设置完成");
            return result;
        };

        // Hook 2: 设置接收人 (J1方法)
        MessageClass.J1.implementation = function(talker) {
            console.log("\n" + "=".repeat(50));
            console.log("[接收人设置] J1 被调用");
            console.log(`[+] 接收人ID: ${talker}`);
            console.log(`[+] 消息对象: ${this}`);

            // 调用原方法
            var result = this.J1(talker);
            console.log("[+] 接收人设置完成");
            return result;
        };

        // Hook 3: 设置创建时间 (e1方法)
        MessageClass.e1.implementation = function(timestamp) {
            console.log("\n" + "=".repeat(50));
            console.log("[创建时间设置] e1 被调用");
            console.log(`[+] 时间戳: ${timestamp}`);
            console.log(`[+] 格式化时间: ${formatTimestamp(timestamp)}`);
            console.log(`[+] 消息对象: ${this}`);

            // 调用原方法
            var result = this.e1(timestamp);
            console.log("[+] 创建时间设置完成");
            return result;
        };

        // Hook 4: 设置消息类型
        MessageClass.setType.implementation = function(type) {
            console.log("\n" + "=".repeat(50));
            console.log("[消息类型设置] setType 被调用");
            console.log(`[+] 消息类型: ${type} (${getMessageTypeDesc(type)})`);
            console.log(`[+] 消息对象: ${this}`);

            // 更新统计
            messageStats.totalMessages++;
            switch (type) {
                case 1: messageStats.textMessages++; break;
                case 3: messageStats.imageMessages++; break;
                case 34: messageStats.voiceMessages++; break;
                case 43: messageStats.videoMessages++; break;
                default: messageStats.otherMessages++; break;
            }

            // 调用原方法
            var result = this.setType(type);
            console.log("[+] 消息类型设置完成");
            return result;
        };

        // Hook 5: 设置发送标识 (n1方法)
        MessageClass.n1.implementation = function(isSend) {
            console.log("\n" + "=".repeat(50));
            console.log("[发送标识设置] n1 被调用");
            console.log(`[+] 是否发送: ${isSend} (${isSend === 1 ? '发送' : '接收'})`);
            console.log(`[+] 消息对象: ${this}`);

            // 调用原方法
            var result = this.n1(isSend);
            console.log("[+] 发送标识设置完成");
            return result;
        };

        // Hook 6: 获取消息内容
        MessageClass.getContent.implementation = function() {
            var content = this.getContent();
            console.log("\n[获取消息内容] getContent 被调用");
            console.log(`[+] 返回内容: ${content}`);
            return content;
        };

        // Hook 7: 获取创建时间
        MessageClass.getCreateTime.implementation = function() {
            var time = this.getCreateTime();
            console.log("\n[获取创建时间] getCreateTime 被调用");
            console.log(`[+] 返回时间: ${time} (${formatTimestamp(time)})`);
            return time;
        };

        console.log("[+] 消息对象Hook设置完成");

    } catch (e) {
        console.log(`[-] Hook消息对象失败: ${e}`);
    }

    try {
        // ==================== 第二部分：消息发送核心Hook ====================
        console.log("\n[*] 开始Hook消息发送核心类 et0.o...");

        var SenderClass = Java.use("et0.o");
        console.log("[+] 成功找到发送类: et0.o");

        // Hook 核心发送方法 i
        SenderClass.i.implementation = function(messageContainer, config, continuation) {
            console.log("\n" + "=".repeat(60));
            console.log("🚀 [核心发送方法] mo62223i 被调用");
            console.log("=".repeat(60));
            console.log(`[+] 消息容器: ${messageContainer}`);
            console.log(`[+] 配置对象: ${config}`);
            console.log(`[+] 协程回调: ${continuation}`);

            // 尝试获取消息容器中的消息信息
            try {
                if (messageContainer) {
                    console.log(`[+] 消息容器类型: ${messageContainer.getClass().getName()}`);
                    // 可以尝试获取更多信息
                }
            } catch (e) {
                console.log(`[-] 获取消息容器信息失败: ${e}`);
            }

            // 打印详细的调用堆栈
            printStackTrace("核心发送方法");

            console.log("[*] 准备调用原始发送方法...");
            var result = this.i(messageContainer, config, continuation);
            console.log("[+] 原始发送方法调用完成");
            console.log(`[+] 返回结果: ${result}`);

            return result;
        };

        console.log("[+] 消息发送核心Hook设置完成");

    } catch (e) {
        console.log(`[-] Hook消息发送核心失败: ${e}`);
    }

    try {
        // ==================== 第三部分：Pipeline处理Hook ====================
        console.log("\n[*] 开始Hook Pipeline处理组件...");

        // Hook Pipeline组件 np4.q
        try {
            var PipelineClass1 = Java.use("np4.q");
            console.log("[+] 成功找到Pipeline类: np4.q");

            // 如果有invoke方法，Hook它
            if (PipelineClass1.invoke) {
                PipelineClass1.invoke.implementation = function() {
                    console.log("\n[Pipeline-np4.q] invoke 被调用");
                    console.log(`[+] 参数数量: ${arguments.length}`);
                    for (var i = 0; i < arguments.length; i++) {
                        console.log(`[+] 参数${i}: ${arguments[i]}`);
                    }

                    var result = this.invoke.apply(this, arguments);
                    console.log(`[+] Pipeline处理结果: ${result}`);
                    return result;
                };
            }
        } catch (e) {
            console.log(`[-] Hook Pipeline np4.q 失败: ${e}`);
        }

        // Hook Pipeline组件 vp4.c0
        try {
            var PipelineClass2 = Java.use("vp4.c0");
            console.log("[+] 成功找到Pipeline类: vp4.c0");

            // Hook可能的处理方法
            if (PipelineClass2.invoke) {
                PipelineClass2.invoke.implementation = function() {
                    console.log("\n[Pipeline-vp4.c0] invoke 被调用");
                    console.log(`[+] 参数数量: ${arguments.length}`);

                    var result = this.invoke.apply(this, arguments);
                    console.log(`[+] Pipeline处理结果: ${result}`);
                    return result;
                };
            }
        } catch (e) {
            console.log(`[-] Hook Pipeline vp4.c0 失败: ${e}`);
        }

        console.log("[+] Pipeline处理Hook设置完成");

    } catch (e) {
        console.log(`[-] Hook Pipeline处理失败: ${e}`);
    }

    try {
        // ==================== 第四部分：UI交互Hook ====================
        console.log("\n[*] 开始Hook UI交互组件...");

        // Hook ChatFooter发送按钮点击
        try {
            var ChatFooterClass = Java.use("com.tencent.mm.pluginsdk.ui.chat.ChatFooter");
            console.log("[+] 成功找到ChatFooter类");

            // Hook A方法（发送按钮处理）
            if (ChatFooterClass.A) {
                ChatFooterClass.A.implementation = function() {
                    console.log("\n" + "=".repeat(50));
                    console.log("🎯 [UI交互] ChatFooter.A 被调用 (发送按钮点击)");
                    console.log("=".repeat(50));

                    printStackTrace("UI发送按钮点击");

                    var result = this.A.apply(this, arguments);
                    console.log("[+] 发送按钮处理完成");
                    return result;
                };
            }
        } catch (e) {
            console.log(`[-] Hook ChatFooter 失败: ${e}`);
        }

        // Hook EditText输入框
        try {
            var EditTextClass = Java.use("com.tencent.mm.ui.widget.MMEditText");
            console.log("[+] 成功找到MMEditText类");

            // Hook getText方法（处理重载）
            EditTextClass.getText.overload().implementation = function() {
                var text = this.getText();
                if (text && text.toString().length > 0) {
                    console.log(`\n[输入框内容] getText: ${text.toString()}`);
                }
                return text;
            };
        } catch (e) {
            console.log(`[-] Hook MMEditText 失败: ${e}`);
        }

        console.log("[+] UI交互Hook设置完成");

    } catch (e) {
        console.log(`[-] Hook UI交互失败: ${e}`);
    }

    try {
        // ==================== 第五部分：数据库操作Hook ====================
        console.log("\n[*] 开始Hook数据库操作...");

        // Hook 消息数据库转换方法
        var MessageClass = Java.use("qk.t7");

        // Hook convertTo方法（转换为ContentValues）
        if (MessageClass.convertTo) {
            MessageClass.convertTo.implementation = function() {
                console.log("\n[数据库操作] convertTo 被调用");
                console.log("[+] 准备将消息对象转换为数据库格式");

                var contentValues = this.convertTo();

                // 尝试打印ContentValues内容
                try {
                    console.log("[+] 数据库字段信息:");
                    var keySet = contentValues.keySet();
                    var iterator = keySet.iterator();
                    while (iterator.hasNext()) {
                        var key = iterator.next();
                        var value = contentValues.get(key);
                        console.log(`    ${key}: ${value}`);
                    }
                } catch (e) {
                    console.log(`[-] 读取ContentValues失败: ${e}`);
                }

                return contentValues;
            };
        }

        console.log("[+] 数据库操作Hook设置完成");

    } catch (e) {
        console.log(`[-] Hook数据库操作失败: ${e}`);
    }

    // 网络请求Hook已跳过（微信可能使用自定义网络库）
    console.log("\n[*] 跳过网络请求Hook（微信使用自定义网络库）");

    // ==================== 第七部分：消息统计和监控 ====================
    var messageStats = {
        totalMessages: 0,
        textMessages: 0,
        imageMessages: 0,
        voiceMessages: 0,
        videoMessages: 0,
        otherMessages: 0,
        startTime: Date.now()
    };

    // 定期打印统计信息
    setInterval(function() {
        if (messageStats.totalMessages > 0) {
            console.log("\n" + "=".repeat(40));
            console.log("📊 消息发送统计信息");
            console.log("=".repeat(40));
            console.log(`总消息数: ${messageStats.totalMessages}`);
            console.log(`文本消息: ${messageStats.textMessages}`);
            console.log(`图片消息: ${messageStats.imageMessages}`);
            console.log(`语音消息: ${messageStats.voiceMessages}`);
            console.log(`视频消息: ${messageStats.videoMessages}`);
            console.log(`其他消息: ${messageStats.otherMessages}`);
            console.log(`运行时间: ${Math.floor((Date.now() - messageStats.startTime) / 1000)}秒`);
            console.log("=".repeat(40));
        }
    }, 30000); // 每30秒打印一次

    // 消息统计会在setType Hook中自动更新

    console.log("\n" + "=".repeat(60));
    console.log("🎉 微信消息发送Hook脚本初始化完成！");
    console.log("=".repeat(60));
    console.log("📝 Hook覆盖范围:");
    console.log("   ✅ 消息对象构造和属性设置 (qk.t7)");
    console.log("   ✅ 核心消息发送方法 (et0.o.i)");
    console.log("   ✅ Pipeline处理流程 (np4.q, vp4.c0)");
    console.log("   ✅ UI交互事件 (ChatFooter.A)");
    console.log("   ✅ 数据库操作 (convertTo)");
    console.log("   ✅ 消息统计和监控");
    console.log("=".repeat(60));
    console.log("🔍 现在可以发送微信消息来查看完整的调用流程！");
    console.log("=".repeat(60));

});