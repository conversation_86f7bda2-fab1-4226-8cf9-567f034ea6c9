#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB管理器
负责ADB设备的连接、管理和命令执行
"""

import subprocess
import threading
import time
import socket
import re
import sys
from typing import List, Dict, Optional, Callable
import adbutils
import requests
import os
import cv2
import numpy as np
from PIL import Image, ImageQt
import io
from PyQt5.QtCore import QTimer, QObject, pyqtSignal, QThread
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QImage

# 添加屏幕捕获线程类
class ScreenCaptureThread(QThread):
    """屏幕捕获线程"""
    screen_updated = pyqtSignal(object)
    
    def __init__(self, adb_device, parent=None):
        super().__init__(parent)
        self.adb_device = adb_device
        self.running = False
        self.fps_limit = 30  # 提高到30FPS
        self.frame_interval = 1.0 / self.fps_limit
        self.last_frame_time = 0
    
    def run(self):
        self.running = True
        while self.running:
            try:
                # 帧率控制
                current_time = time.time()
                elapsed = current_time - self.last_frame_time
                
                if elapsed >= self.frame_interval:
                    # 使用adbutils的screenshot方法
                    image = self.adb_device.screenshot()
                    if image and self.running:
                        self.screen_updated.emit(image)
                        self.last_frame_time = current_time
                    
                    # 动态调整休眠时间
                    processing_time = time.time() - current_time
                    sleep_time = max(0.001, self.frame_interval - processing_time)
                    time.sleep(sleep_time)
                else:
                    # 短暂休眠避免CPU占用过高
                    time.sleep(0.001)
                    
            except Exception as e:
                print(f"屏幕捕获错误: {e}")
                time.sleep(0.5)  # 出错时短暂休眠
    
    def stop(self):
        self.running = False
        self.wait(1000)  # 等待线程结束，最多1秒

class DeviceInfo:
    """设备信息类"""
    def __init__(self, serial: str, status: str, product: str = "", model: str = "", device: str = ""):
        self.serial = serial
        self.status = status
        self.product = product
        self.model = model
        self.device = device
        self.brand = ""
        self.android_version = ""
        self.api_level = ""
        self.connection_type = "USB"  # 只支持USB连接
        
    def __str__(self):
        return f"{self.serial} - {self.status} ({self.brand} {self.model})"

class ADBManager:
    """ADB管理器"""
    
    def __init__(self):
        self.adb_client = None
        self.connected_devices: Dict[str, adbutils.AdbDevice] = {}
        self.device_info_cache: Dict[str, DeviceInfo] = {}
        self.logcat_processes: Dict[str, subprocess.Popen] = {}
        self.screen_capture_thread = None
        self.frida_server_processes: Dict[str, subprocess.Popen] = {}
        
    def initialize_adb(self) -> bool:
        """初始化ADB客户端"""
        try:
            # 启动ADB服务器
            subprocess.run(['adb', 'start-server'], check=True, capture_output=True)
            
            # 连接到ADB客户端
            self.adb_client = adbutils.AdbClient(host="127.0.0.1", port=5037)
            return True
        except Exception as e:
            print(f"初始化ADB失败: {e}")
            return False
    
    def get_devices(self) -> List[DeviceInfo]:
        """获取所有连接的设备"""
        devices = []
        
        if not self.adb_client:
            if not self.initialize_adb():
                return devices
        
        try:
            # 获取ADB设备列表
            adb_devices = self.adb_client.device_list()
            
            for device in adb_devices:
                serial = device.serial
                
                # 从缓存获取或创建设备信息
                if serial not in self.device_info_cache:
                    device_info = DeviceInfo(serial, "device")
                    self._update_device_info(device, device_info)
                    self.device_info_cache[serial] = device_info
                
                devices.append(self.device_info_cache[serial])
                self.connected_devices[serial] = device
                
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            
        return devices
    
    def _update_device_info(self, device: adbutils.AdbDevice, device_info: DeviceInfo):
        """更新设备详细信息"""
        try:
            # 获取设备属性
            brand = device.shell("getprop ro.product.brand").strip()
            model = device.shell("getprop ro.product.model").strip()
            android_version = device.shell("getprop ro.build.version.release").strip()
            api_level = device.shell("getprop ro.build.version.sdk").strip()
            
            device_info.brand = brand
            device_info.model = model
            device_info.android_version = android_version
            device_info.api_level = api_level
            
            # 只支持USB连接
            device_info.connection_type = "USB"
                
        except Exception as e:
            print(f"更新设备信息失败: {e}")
    
    def connect_device(self, serial: str) -> bool:
        """连接到指定设备（仅USB）"""
        try:
            if serial in self.connected_devices:
                return True
            
            # 刷新设备列表
            self.get_devices()
            return serial in self.connected_devices
            
        except Exception as e:
            print(f"连接设备失败: {e}")
            return False
    
    def disconnect_device(self, serial: str) -> bool:
        """断开设备连接"""
        try:
            # 从连接列表中移除
            if serial in self.connected_devices:
                del self.connected_devices[serial]
                
            return True
            
        except Exception as e:
            print(f"断开设备连接失败: {e}")
            return False
    
    def get_screen_size(self, serial: str) -> tuple:
        """获取设备屏幕尺寸"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                size = device.window_size()
                return size
            return (1080, 1920)  # 默认尺寸
        except Exception as e:
            print(f"获取屏幕尺寸失败: {e}")
            return (1080, 1920)
    
    def capture_screen(self, serial: str) -> Image.Image:
        """截取设备屏幕"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                # 使用adbutils的screenshot方法
                pil_image = device.screenshot()
                return pil_image
            return None
        except Exception as e:
            print(f"截屏失败: {e}")
            return None
    
    def start_screen_capture(self, serial: str, callback: Callable[[Image.Image], None]):
        """开始实时屏幕捕获"""
        # 停止之前的捕获
        self.stop_screen_capture()
        
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                
                # 创建屏幕捕获线程
                self.screen_capture_thread = ScreenCaptureThread(device)
                
                # 连接信号
                self.screen_capture_thread.screen_updated.connect(callback)
                
                # 启动线程
                self.screen_capture_thread.start()
                return True
            return False
        except Exception as e:
            print(f"启动屏幕捕获失败: {e}")
            return False
    
    def stop_screen_capture(self):
        """停止屏幕捕获"""
        if self.screen_capture_thread and self.screen_capture_thread.isRunning():
            self.screen_capture_thread.stop()
            self.screen_capture_thread = None
    
    def click_screen(self, serial: str, x: int, y: int):
        """点击屏幕"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                device.click(x, y)
        except Exception as e:
            print(f"点击失败: {e}")
    
    def swipe_screen(self, serial: str, x1: int, y1: int, x2: int, y2: int, duration: float = 0.5):
        """滑动屏幕"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                device.swipe(x1, y1, x2, y2, duration)
        except Exception as e:
            print(f"滑动失败: {e}")
    
    def send_key(self, serial: str, key: str):
        """发送按键"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                device.keyevent(key)
        except Exception as e:
            print(f"发送按键失败: {e}")
    
    def send_text(self, serial: str, text: str):
        """发送文本"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                device.send_keys(text)
        except Exception as e:
            print(f"发送文本失败: {e}")
    
    def start_logcat(self, serial: str, callback: Callable[[str], None], 
                    filter_tag: str = "", filter_level: str = ""):
        """启动logcat日志监控"""
        if serial in self.logcat_processes:
            self.stop_logcat(serial)
        
        try:
            # 使用更详细的格式化选项，包括时间戳、PID、TID、优先级、标签和消息
            cmd = ['adb', '-s', serial, 'logcat', '-v', 'threadtime']
            
            # 添加过滤器
            if filter_level:
                cmd.extend([f'*:{filter_level}'])
            if filter_tag:
                cmd.extend(['-s', filter_tag])
            
            print(f"Logcat命令: {' '.join(cmd)}")
            
            # 使用PIPE而不是通过shell读取，以确保正确的编码处理
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace'  # 替换无法解码的字符
            )
            
            self.logcat_processes[serial] = process
            
            # 启动读取线程
            def read_logcat():
                try:
                    while process.poll() is None:
                        line = process.stdout.readline()
                        if line:
                            try:
                                # 处理和清理日志行
                                cleaned_line = line.strip()
                                if cleaned_line:  # 确保不是空行
                                    callback(cleaned_line)
                            except Exception as e:
                                print(f"处理日志行失败: {e}")
                except Exception as e:
                    print(f"读取logcat失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            thread = threading.Thread(target=read_logcat)
            thread.daemon = True
            thread.start()
            
            return True
            
        except Exception as e:
            print(f"启动logcat失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def stop_logcat(self, serial: str):
        """停止logcat监控"""
        if serial in self.logcat_processes:
            try:
                process = self.logcat_processes[serial]
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
            finally:
                del self.logcat_processes[serial]
    
    def execute_command(self, serial: str, command: str) -> str:
        """在设备上执行命令"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                return device.shell(command)
            else:
                result = subprocess.run(['adb', '-s', serial, 'shell', command], 
                                      capture_output=True, text=True)
                return result.stdout
        except Exception as e:
            return f"执行命令失败: {e}"
    
    def install_apk(self, serial: str, apk_path: str) -> bool:
        """安装APK文件"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                device.install(apk_path)
                return True
            return False
        except Exception as e:
            print(f"安装APK失败: {e}")
            return False
    
    def push_file(self, serial: str, local_path: str, remote_path: str = None) -> bool:
        """推送文件到设备"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                if remote_path is None:
                    # 默认推送到下载目录
                    filename = os.path.basename(local_path)
                    remote_path = f"/sdcard/Download/{filename}"
                
                device.sync.push(local_path, remote_path)
                return True
            return False
        except Exception as e:
            print(f"推送文件失败: {e}")
            return False
    
    def pull_file(self, serial: str, remote_path: str, local_path: str) -> bool:
        """从设备拉取文件"""
        try:
            if serial in self.connected_devices:
                device = self.connected_devices[serial]
                device.sync.pull(remote_path, local_path)
                return True
            return False
        except Exception as e:
            print(f"拉取文件失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        # 停止所有logcat进程
        for serial in list(self.logcat_processes.keys()):
            self.stop_logcat(serial)
        
        # 停止所有frida服务器进程
        for serial in list(self.frida_server_processes.keys()):
            self.stop_frida_server(serial)
        
        # 清理缓存
        self.connected_devices.clear()
        self.device_info_cache.clear()
        
    def check_lib_files(self, serial: str) -> dict:
        """检查设备目录下是否存在特定库文件
        
        检查 /data/local/tmp/lib64 目录下是否存在以下文件：
        - libcall (函数参数跟踪)
        - libcapture (自动抓包)
        - libtrace (函数跟踪)
        
        初次检查时，如果发现带.so后缀的文件，会自动将其改为不带后缀
        
        Returns:
            dict: 包含三个文件的状态，键为文件名，值为布尔值表示是否启用（有.so后缀）
        """
        try:
            if serial not in self.connected_devices:
                return {}
            
            # 检查目录是否存在
            result = self.execute_command(serial, "ls -la /data/local/tmp/xiaojianbang/lib64")
            if "No such file or directory" in result:
                return {}
            
            # 检查各个文件
            files_status = {
                "libcall": False,
                "libcapture": False,
                "libtrace": False
            }
            
            # 检查每个文件是否存在以及是否有.so后缀
            for file_name in files_status.keys():
                # 检查不带.so后缀的文件
                result = self.execute_command(serial, f"ls -la /data/local/tmp/xiaojianbang/lib64/{file_name}")
                if "No such file or directory" not in result:
                    # 文件存在但未启用
                    files_status[file_name] = False
                    continue
                
                # 检查带.so后缀的文件
                result = self.execute_command(serial, f"ls -la /data/local/tmp/xiaojianbang/lib64/{file_name}.so")
                if "No such file or directory" not in result:
                    # 文件存在且已启用，初次检查时将其改为不带后缀
                    src_path = f"/data/local/tmp/xiaojianbang/lib64/{file_name}.so"
                    dst_path = f"/data/local/tmp/xiaojianbang/lib64/{file_name}"
                    
                    # 重命名文件
                    self.execute_command(serial, f"mv {src_path} {dst_path}")
                    
                    # 验证操作是否成功
                    result = self.execute_command(serial, f"ls -la {dst_path}")
                    if "No such file or directory" not in result:
                        files_status[file_name] = False
                        print(f"已将 {file_name}.so 重命名为 {file_name}")
            
            return files_status
            
        except Exception as e:
            print(f"检查库文件失败: {e}")
            return {}
    
    def toggle_lib_file(self, serial: str, file_name: str, enable: bool) -> bool:
        # File paths
        src_path = f"/data/local/tmp/xiaojianbang/lib64/{file_name}"
        dst_path = f"/data/local/tmp/xiaojianbang/lib64/{file_name}.so"
        
        if enable:
            # Enable: Add .so extension
            self.execute_command(serial, f"su -c 'mv {src_path} {dst_path}'")
            # Verify success
            result = self.execute_command(serial, f"su -c 'ls -la {dst_path}'")
            return "No such file or directory" not in result
            
        else:
            # Disable: Remove .so extension
            self.execute_command(serial, f"su -c 'mv {dst_path} {src_path}'")
            # Verify success
            result = self.execute_command(serial, f"su -c 'ls -la {src_path}'")
            return "No such file or directory" not in result

    # ---------- 以下是Frida相关功能 ----------
    
    def download_frida_server(self, version: str = "16.2.1") -> str:
        """下载适合Android 13 Pixel 4的Frida服务器
        
        Args:
            version: Frida版本号，默认为16.2.1
            
        Returns:
            str: 下载的Frida服务器本地路径
        """
        try:
            # 为Pixel 4 (arm64)创建下载目录
            download_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "tools", "frida")
            os.makedirs(download_dir, exist_ok=True)
            
            # 构建URL (适用于arm64架构)
            frida_url = f"https://github.com/frida/frida/releases/download/{version}/frida-server-{version}-android-arm64.xz"
            
            # 构建本地文件路径
            local_xz_path = os.path.join(download_dir, f"frida-server-{version}-android-arm64.xz")
            local_server_path = os.path.join(download_dir, f"frida-server-{version}-android-arm64")
            
            # 如果已解压的文件存在，直接返回路径
            if os.path.exists(local_server_path):
                print(f"Frida服务器已存在: {local_server_path}")
                return local_server_path
                
            # 如果XZ文件不存在，下载它
            if not os.path.exists(local_xz_path):
                print(f"正在下载Frida服务器: {frida_url}")
                response = requests.get(frida_url, stream=True)
                response.raise_for_status()
                
                with open(local_xz_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
            
            # 解压XZ文件
            print(f"正在解压Frida服务器...")
            import lzma
            with lzma.open(local_xz_path) as f_in:
                with open(local_server_path, 'wb') as f_out:
                    f_out.write(f_in.read())
            
            # 确保文件是可执行的
            os.chmod(local_server_path, 0o755)
            
            return local_server_path
            
        except Exception as e:
            print(f"下载Frida服务器失败: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    def install_frida_server(self, serial: str, version: str = "16.2.1") -> bool:
        """安装Frida服务器到设备
        
        Args:
            serial: 设备序列号
            version: Frida版本号，默认为16.2.1
            
        Returns:
            bool: 安装是否成功
        """
        try:
            # 下载Frida服务器
            server_path = self.download_frida_server(version)
            if not server_path:
                return False
            
            # 检查设备是否已root
            is_rooted = self._check_device_root(serial)
            if not is_rooted:
                print("设备未root，Frida可能无法正常工作")
            
            # 推送Frida服务器到设备
            remote_path = "/data/local/tmp/frida-server"
            if not self.push_file(serial, server_path, remote_path):
                return False
            
            # 设置可执行权限
            self.execute_command(serial, f"chmod 755 {remote_path}")
            
            print(f"Frida服务器已安装到: {remote_path}")
            return True
            
        except Exception as e:
            print(f"安装Frida服务器失败: {e}")
            return False
    
    def _check_device_root(self, serial: str) -> bool:
        """检查设备是否已root
        
        Args:
            serial: 设备序列号
            
        Returns:
            bool: 设备是否已root
        """
        try:
            result = self.execute_command(serial, "which su")
            return "su" in result and "not found" not in result.lower()
        except:
            return False
    
    def start_frida_server(self, serial: str) -> bool:
        """启动设备上的Frida服务器
        
        Args:
            serial: 设备序列号
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 如果服务器已在运行，先停止它
            self.stop_frida_server(serial)
            
            # 检查frida-server是否存在
            result = self.execute_command(serial, "ls -la /data/local/tmp/frida-server")
            if "No such file or directory" in result:
                print("Frida服务器未安装，请先安装")
                return False
            
            # 检查设备是否已root
            is_rooted = self._check_device_root(serial)
            
            # 启动frida-server
            cmd = ['adb', '-s', serial, 'shell']
            
            if is_rooted:
                cmd.extend(["su", "-c", "/data/local/tmp/frida-server -D"])
            else:
                cmd.extend(["/data/local/tmp/frida-server", "-D"])
                
            print(f"启动Frida服务器: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待片刻，确保服务器启动
            time.sleep(2)
            
            # 检查进程是否仍在运行
            if process.poll() is None:
                # 进程仍在运行，保存它
                self.frida_server_processes[serial] = process
                return True
            else:
                # 进程已退出，检查错误
                stderr = process.stderr.read().decode('utf-8', errors='replace')
                print(f"Frida服务器启动失败: {stderr}")
                return False
                
        except Exception as e:
            print(f"启动Frida服务器失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def stop_frida_server(self, serial: str) -> bool:
        """停止设备上的Frida服务器
        
        Args:
            serial: 设备序列号
            
        Returns:
            bool: 停止是否成功
        """
        try:
            # 如果有保存的进程对象，先终止它
            if serial in self.frida_server_processes:
                process = self.frida_server_processes[serial]
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    try:
                        process.kill()
                    except:
                        pass
                finally:
                    del self.frida_server_processes[serial]
            
            # 杀死设备上的frida-server进程
            self.execute_command(serial, "pkill -9 frida-server")
            
            # 等待片刻确保进程已停止
            time.sleep(1)
            
            # 检查进程是否仍在运行
            result = self.execute_command(serial, "ps -A | grep frida-server")
            if "frida-server" in result:
                print("Frida服务器仍在运行，尝试以root身份杀死它")
                self.execute_command(serial, "su -c 'pkill -9 frida-server'")
                time.sleep(1)
                
                # 再次检查
                result = self.execute_command(serial, "ps -A | grep frida-server")
                if "frida-server" in result:
                    print("无法停止Frida服务器")
                    return False
            
            return True
            
        except Exception as e:
            print(f"停止Frida服务器失败: {e}")
            return False
    
    def list_running_apps(self, serial: str) -> List[Dict[str, str]]:
        """获取设备上运行的应用列表
        
        Args:
            serial: 设备序列号
            
        Returns:
            List[Dict[str, str]]: 应用列表，每个应用包含pid和package_name
        """
        try:
            # 获取运行中的应用
            result = self.execute_command(serial, "ps -A | grep -E 'app_|u0_a'")
            lines = result.strip().split('\n')
            
            apps = []
            for line in lines:
                parts = line.split()
                if len(parts) >= 9:  # 确保有足够的字段
                    pid = parts[1]
                    # 尝试从cmdline获取包名
                    cmdline = self.execute_command(serial, f"cat /proc/{pid}/cmdline 2>/dev/null")
                    package_name = cmdline.strip().replace('\x00', '')
                    
                    if package_name and package_name != "cat" and "not found" not in package_name:
                        apps.append({
                            "pid": pid,
                            "package_name": package_name
                        })
            
            return apps
            
        except Exception as e:
            print(f"获取运行中的应用失败: {e}")
            return []
    
    def ensure_frida_server_running(self, serial: str) -> bool:
        """确保Frida服务器正在运行
        
        Args:
            serial: 设备序列号
            
        Returns:
            bool: Frida服务器是否正在运行
        """
        try:
            # 检查frida-server进程是否存在
            result = self.execute_command(serial, "ps -A | grep frida-server")
            if "frida-server" in result:
                print("检测到Frida服务器已在运行，尝试先停止它")
                # 尝试停止现有的frida-server
                self.stop_frida_server(serial)
                # 等待服务器停止
                time.sleep(2)
            
            # 尝试启动服务器
            print("正在启动Frida服务器...")
            # 使用-l 0.0.0.0参数，允许远程连接
            start_cmd = f"su -c '/data/local/tmp/frida-server -l 0.0.0.0 -D'"
            self.execute_command(serial, start_cmd)
            
            # 等待服务器启动
            time.sleep(3)
            
            # 再次检查
            result = self.execute_command(serial, "ps -A | grep frida-server")
            if "frida-server" in result:
                print("Frida服务器已成功启动")
                # 设置端口转发
                try:
                    subprocess.run(['adb', '-s', serial, 'forward', 'tcp:27042', 'tcp:27042'], check=True)
                    print("端口转发已设置: tcp:27042 -> tcp:27042")
                except Exception as e:
                    print(f"设置端口转发时出错: {e}")
                return True
            else:
                print("Frida服务器启动失败")
                return False
                
        except Exception as e:
            print(f"检查Frida服务器状态时出错: {e}")
            return False
            
    def execute_frida_script(self, serial: str, script_path: str, package_name: str) -> bool:
        """执行Frida脚本
        
        Args:
            serial: 设备序列号
            script_path: 脚本路径
            package_name: 目标应用包名
            
        Returns:
            bool: 执行是否成功
        """
        try:
            # 确保Frida服务器正在运行
            if not self.ensure_frida_server_running(serial):
                return False
            
            # 检查脚本是否存在
            if not os.path.exists(script_path):
                print(f"脚本不存在: {script_path}")
                return False
            
            # 确保frida模块已安装
            try:
                # 创建一个临时脚本来检查frida是否已安装
                check_script = """
import importlib.util
import sys
import subprocess

# 检查frida模块
if importlib.util.find_spec("frida") is None:
    print("正在安装frida...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "frida", "frida-tools"])
    print("frida安装完成")
else:
    print("frida已安装")
"""
                # 执行检查脚本
                subprocess.check_call([sys.executable, "-c", check_script])
                
                # 将设备序列号传递给脚本
                cmd = [
                    sys.executable, "-c",
                    f"""
import frida
import sys
import time
import os

def on_message(message, data):
    if message['type'] == 'send':
        print("[*] {{0}}".format(message['payload']))
    else:
        print(message)

try:
    print("正在读取脚本文件: {script_path}")
    with open("{script_path}", "r", encoding="utf-8") as f:
        jscode = f.read()
    
    print("正在连接到设备...")
    
    # 尝试连接到设备
    device = None
    
    # 1. 尝试通过本地连接 (adb forward tcp:27042 tcp:27042)
    try:
        print("尝试设置端口转发...")
        import subprocess
        subprocess.run(['adb', '-s', '{serial}', 'forward', 'tcp:27042', 'tcp:27042'], check=True)
        print("尝试通过本地连接...")
        device = frida.get_device_manager().add_remote_device("127.0.0.1:27042")
        print("成功通过本地连接")
    except Exception as e:
        print(f"通过本地连接失败: {{e}}")
    
    # 2. 尝试通过设备ID直接连接
    if device is None:
        try:
            device_id = "{serial}"
            print(f"尝试通过设备ID连接: {{device_id}}")
            device = frida.get_device(device_id, timeout=5)
            print(f"成功通过设备ID连接: {{device_id}}")
        except Exception as e:
            print(f"通过设备ID连接失败: {{e}}")
    
    # 3. 尝试通过USB连接
    if device is None:
        try:
            print("尝试通过USB连接...")
            device = frida.get_usb_device(timeout=5)
            print("成功通过USB连接")
        except Exception as e:
            print(f"通过USB连接失败: {{e}}")
    
    # 4. 尝试枚举所有设备
    if device is None:
        try:
            print("尝试枚举所有设备...")
            devices = frida.enumerate_devices()
            if devices:
                for d in devices:
                    print(f"找到设备: {{d.id}} ({{d.type}})")
                # 选择第一个远程设备
                remote_devices = [d for d in devices if d.type == 'remote']
                if remote_devices:
                    device = remote_devices[0]
                    print(f"使用远程设备: {{device.id}}")
                else:
                    # 如果没有远程设备，使用第一个设备
                    device = devices[0]
                    print(f"使用找到的第一个设备: {{device.id}}")
            else:
                print("没有找到任何设备")
        except Exception as e:
            print(f"枚举设备失败: {{e}}")
    
    # 如果仍然没有找到设备，抛出异常
    if device is None:
        raise Exception("无法连接到任何Frida设备")
    
    print(f"成功连接到设备: {{device.id}}")
    print(f"正在处理应用: {package_name}")
    
    # 获取进程ID
    pid = None
    
    # 1. 尝试获取已运行的进程
    try:
        print(f"尝试获取已运行的进程: {package_name}")
        # 列出所有进程
        processes = device.enumerate_processes()
        print(f"设备上有 {{len(processes)}} 个进程")
        
        # 查找目标进程
        target_process = None
        for process in processes:
            if process.name == "{package_name}":
                target_process = process
                break
                
        if target_process:
            pid = target_process.pid
            print(f"找到已运行的进程: {{pid}} ({{target_process.name}})")
        else:
            print(f"未找到进程: {package_name}")
    except Exception as e:
        print(f"获取已运行进程失败: {{e}}")
    
    # 2. 如果进程未运行，尝试启动它
    if pid is None:
        try:
            print(f"尝试启动进程: {package_name}")
            pid = device.spawn(["{package_name}"])
            print(f"成功启动进程: {{pid}}")
            # 标记为需要恢复执行
            need_resume = True
        except Exception as e:
            print(f"启动进程失败: {{e}}")
            # 尝试使用shell命令启动应用
            try:
                print("尝试使用shell命令启动应用...")
                launch_cmd = f"am start -n {package_name}/$(cmd package resolve-activity --brief {package_name} | tail -n 1)"
                subprocess.run(['adb', '-s', '{serial}', 'shell', launch_cmd], check=True)
                print("应用已启动，等待3秒...")
                time.sleep(3)
                
                # 再次尝试获取进程
                processes = device.enumerate_processes()
                target_process = None
                for process in processes:
                    if process.name == "{package_name}":
                        target_process = process
                        break
                        
                if target_process:
                    pid = target_process.pid
                    print(f"找到已启动的进程: {{pid}}")
                    need_resume = False
                else:
                    raise Exception(f"无法找到已启动的进程: {package_name}")
            except Exception as e2:
                print(f"使用shell命令启动应用失败: {{e2}}")
                raise Exception(f"无法找到或启动进程: {package_name}")
    else:
        need_resume = False
    
    # 附加到进程
    print(f"正在附加到进程: {{pid}}")
    session = device.attach(pid)
    
    # 创建脚本
    print("正在创建脚本...")
    script = session.create_script(jscode)
    script.on('message', on_message)
    
    # 加载脚本
    print("正在加载脚本...")
    script.load()
    
    # 如果是我们启动的进程，恢复执行
    if need_resume:
        print(f"恢复进程执行: {{pid}}")
        device.resume(pid)
    
    print("脚本执行中，按Ctrl+C停止...")
    sys.stdin.read()
except Exception as e:
    print(f"执行Frida脚本失败: {{e}}")
    import traceback
    traceback.print_exc()
"""
                ]
                
                print("执行Frida脚本...")
                # 使用subprocess.Popen启动进程，不等待其完成
                process = subprocess.Popen(cmd)
                
                # 设置一个短暂的等待时间，检查进程是否立即失败
                time.sleep(1)
                if process.poll() is not None:
                    # 进程已经退出
                    returncode = process.returncode
                    if returncode != 0:
                        print(f"Frida脚本进程异常退出，返回码: {returncode}")
                        return False
                
                return True
                
            except Exception as e:
                print(f"安装或执行Frida时出错: {e}")
                import traceback
                traceback.print_exc()
                return False
            
        except Exception as e:
            print(f"执行Frida脚本失败: {e}")
            import traceback
            traceback.print_exc()
            return False
