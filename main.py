#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB 工具主程序
功能：设备管理、日志查看、Android Studio集成
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QMetaType
from PyQt5.QtGui import QTextCursor
from gui.main_window import MainWindow

def main():
    """主程序入口"""
    app = QApplication(sys.argv)
    app.setApplicationName("ADB工具")
    app.setApplicationVersion("1.0.0")
    
    # 注册QTextCursor为元类型，解决信号连接警告
    QMetaType.type("QTextCursor")
    
    # 设置应用图标和样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 