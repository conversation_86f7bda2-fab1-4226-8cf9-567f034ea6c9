/**
 * 微信消息发送Hook脚本 - 精简版
 * 只专注于消息发送流程的核心Hook
 * 基于JADX反编译代码分析，使用原始类名
 */

console.log("🚀 微信消息发送Hook脚本启动");

// 工具函数：格式化时间戳
function formatTimestamp(timestamp) {
    var date = new Date(timestamp);
    return date.toLocaleString('zh-CN');
}

// 工具函数：获取消息类型描述
function getMessageTypeDesc(type) {
    var types = {
        1: "文本消息",
        3: "图片消息", 
        34: "语音消息",
        43: "视频消息",
        47: "表情消息",
        48: "位置消息",
        49: "链接消息",
        10000: "系统消息"
    };
    return types[type] || `未知类型(${type})`;
}

// 工具函数：打印调用堆栈
function printStackTrace(tag) {
    console.log(`[${tag}] 调用堆栈:`);
    var stack = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new());
    console.log(stack);
}

Java.perform(function() {
    console.log("[+] Java环境初始化完成");
    
    // 消息统计
    var messageStats = {
        totalMessages: 0,
        textMessages: 0,
        imageMessages: 0,
        voiceMessages: 0,
        videoMessages: 0,
        otherMessages: 0
    };
    
    try {
        // ==================== 消息对象Hook ====================
        console.log("\n[*] Hook消息对象类 qk.t7...");
        
        var MessageClass = Java.use("qk.t7");
        console.log("[+] 成功找到消息类: qk.t7");
        
        // Hook 1: 设置消息内容 (d1方法)
        MessageClass.d1.implementation = function(content) {
            console.log("\n" + "=".repeat(50));
            console.log("[消息内容设置] d1 被调用");
            console.log(`[+] 消息内容: ${content}`);
            console.log(`[+] 内容长度: ${content ? content.length : 0} 字符`);
            console.log(`[+] 消息对象: ${this}`);
            
            // 调用原方法
            var result = this.d1(content);
            console.log("[+] 消息内容设置完成");
            return result;
        };
        
        // Hook 2: 设置接收人 (J1方法)
        MessageClass.J1.implementation = function(talker) {
            console.log("\n" + "=".repeat(50));
            console.log("[接收人设置] J1 被调用");
            console.log(`[+] 接收人ID: ${talker}`);
            console.log(`[+] 消息对象: ${this}`);
            
            // 调用原方法
            var result = this.J1(talker);
            console.log("[+] 接收人设置完成");
            return result;
        };
        
        // Hook 3: 设置创建时间 (e1方法)
        MessageClass.e1.implementation = function(timestamp) {
            console.log("\n" + "=".repeat(50));
            console.log("[创建时间设置] e1 被调用");
            console.log(`[+] 时间戳: ${timestamp}`);
            console.log(`[+] 格式化时间: ${formatTimestamp(timestamp)}`);
            console.log(`[+] 消息对象: ${this}`);
            
            // 调用原方法
            var result = this.e1(timestamp);
            console.log("[+] 创建时间设置完成");
            return result;
        };
        
        // Hook 4: 设置消息类型
        MessageClass.setType.implementation = function(type) {
            console.log("\n" + "=".repeat(50));
            console.log("[消息类型设置] setType 被调用");
            console.log(`[+] 消息类型: ${type} (${getMessageTypeDesc(type)})`);
            console.log(`[+] 消息对象: ${this}`);
            
            // 调用原方法
            var result = this.setType(type);
            
            // 更新统计
            messageStats.totalMessages++;
            switch (type) {
                case 1: messageStats.textMessages++; break;
                case 3: messageStats.imageMessages++; break;
                case 34: messageStats.voiceMessages++; break;
                case 43: messageStats.videoMessages++; break;
                default: messageStats.otherMessages++; break;
            }
            
            console.log("[+] 消息类型设置完成");
            return result;
        };
        
        // Hook 5: 设置发送标识 (n1方法)
        MessageClass.n1.implementation = function(isSend) {
            console.log("\n" + "=".repeat(50));
            console.log("[发送标识设置] n1 被调用");
            console.log(`[+] 是否发送: ${isSend} (${isSend === 1 ? '发送' : '接收'})`);
            console.log(`[+] 消息对象: ${this}`);
            
            // 调用原方法
            var result = this.n1(isSend);
            console.log("[+] 发送标识设置完成");
            return result;
        };
        
        console.log("[+] 消息对象Hook设置完成");
        
    } catch (e) {
        console.log(`[-] Hook消息对象失败: ${e}`);
    }
    
    try {
        // ==================== 消息发送核心Hook ====================
        console.log("\n[*] Hook消息发送核心类 et0.o...");
        
        var SenderClass = Java.use("et0.o");
        console.log("[+] 成功找到发送类: et0.o");
        
        // Hook 核心发送方法 i
        SenderClass.i.implementation = function(messageContainer, config, continuation) {
            console.log("\n" + "=".repeat(60));
            console.log("🚀 [核心发送方法] i 被调用");
            console.log("=".repeat(60));
            console.log(`[+] 消息容器: ${messageContainer}`);
            console.log(`[+] 配置对象: ${config}`);
            console.log(`[+] 协程回调: ${continuation}`);
            
            // 打印调用堆栈
            printStackTrace("核心发送方法");
            
            console.log("[*] 准备调用原始发送方法...");
            var result = this.i(messageContainer, config, continuation);
            console.log("[+] 原始发送方法调用完成");
            console.log(`[+] 返回结果: ${result}`);
            
            return result;
        };
        
        console.log("[+] 消息发送核心Hook设置完成");
        
    } catch (e) {
        console.log(`[-] Hook消息发送核心失败: ${e}`);
    }
    
    try {
        // ==================== UI交互Hook ====================
        console.log("\n[*] Hook UI交互组件...");
        
        // Hook ChatFooter发送按钮点击
        var ChatFooterClass = Java.use("com.tencent.mm.pluginsdk.ui.chat.ChatFooter");
        console.log("[+] 成功找到ChatFooter类");
        
        // Hook A方法（发送按钮处理）
        ChatFooterClass.A.implementation = function() {
            console.log("\n" + "=".repeat(50));
            console.log("🎯 [UI交互] ChatFooter.A 被调用 (发送按钮点击)");
            console.log("=".repeat(50));
            
            printStackTrace("UI发送按钮点击");
            
            var result = this.A.apply(this, arguments);
            console.log("[+] 发送按钮处理完成");
            return result;
        };
        
        console.log("[+] UI交互Hook设置完成");
        
    } catch (e) {
        console.log(`[-] Hook UI交互失败: ${e}`);
    }
    
    // 定期打印统计信息
    setInterval(function() {
        if (messageStats.totalMessages > 0) {
            console.log("\n" + "=".repeat(40));
            console.log("📊 消息发送统计信息");
            console.log("=".repeat(40));
            console.log(`总消息数: ${messageStats.totalMessages}`);
            console.log(`文本消息: ${messageStats.textMessages}`);
            console.log(`图片消息: ${messageStats.imageMessages}`);
            console.log(`语音消息: ${messageStats.voiceMessages}`);
            console.log(`视频消息: ${messageStats.videoMessages}`);
            console.log(`其他消息: ${messageStats.otherMessages}`);
            console.log("=".repeat(40));
        }
    }, 60000); // 每60秒打印一次
    
    console.log("\n" + "=".repeat(60));
    console.log("🎉 微信消息发送Hook脚本初始化完成！");
    console.log("=".repeat(60));
    console.log("📝 Hook覆盖范围:");
    console.log("   ✅ 消息对象构造和属性设置 (qk.t7)");
    console.log("   ✅ 核心消息发送方法 (et0.o.i)");
    console.log("   ✅ UI交互事件 (ChatFooter.A)");
    console.log("   ✅ 消息统计和监控");
    console.log("=".repeat(60));
    console.log("🔍 现在可以发送微信消息来查看完整的调用流程！");
    console.log("=".repeat(60));
    
});
