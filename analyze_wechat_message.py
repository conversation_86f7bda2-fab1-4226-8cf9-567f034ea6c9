#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信消息发送流程分析脚本
使用JADX MCP接口分析微信发信息的关键类和方法
"""

import requests
import json
import sys

# JADX HTTP API基础URL
JADX_BASE_URL = "http://127.0.0.1:8650"

def get_from_jadx(endpoint, params=None):
    """从JADX获取数据的通用方法"""
    try:
        url = f"{JADX_BASE_URL}/{endpoint}"
        response = requests.get(url, params=params, timeout=60)
        response.raise_for_status()
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def search_classes_by_pattern(pattern):
    """搜索包含特定模式的类"""
    try:
        response = get_from_jadx("all-classes")
        if response:
            data = json.loads(response)
            classes = data.get("classes", [])
            matching_classes = [cls for cls in classes if pattern in cls]
            return matching_classes
    except json.JSONDecodeError:
        print(f"解析类列表失败")
        return []

def get_class_source(class_name):
    """获取指定类的源码"""
    return get_from_jadx("class-source", {"class": class_name})

def get_methods_of_class(class_name):
    """获取类的所有方法"""
    response = get_from_jadx("methods-of-class", {"class": class_name})
    if response:
        return response.splitlines()
    return []

def get_method_source(class_name, method_name):
    """获取指定方法的源码"""
    return get_from_jadx("method-by-name", {"class": class_name, "method": method_name})

def search_method_by_name(method_name):
    """搜索方法名"""
    response = get_from_jadx("search-method", {"method": method_name})
    if response:
        return response.splitlines()
    return []

def analyze_message_classes():
    """分析微信消息相关的关键类"""
    print("=" * 60)
    print("微信消息发送流程分析")
    print("=" * 60)
    
    # 根据文档分析的关键类
    key_classes = [
        "qk.t7",      # 消息对象类
        "et0.o",      # 消息发送核心类  
        "vp4.z",      # 消息容器类
        "vp4.c0",     # Pipeline处理组件
        "np4.q",      # Pipeline处理组件
        "et0.e"       # 消息配置类
    ]
    
    print("\n1. 分析关键类的源码:")
    print("-" * 40)
    
    for class_name in key_classes:
        print(f"\n正在分析类: {class_name}")
        
        # 获取类源码
        source = get_class_source(class_name)
        if source and "error" not in source.lower():
            print(f"✓ 成功获取 {class_name} 源码")
            
            # 保存源码到文件
            filename = f"wechat_analysis_{class_name.replace('.', '_')}.java"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(source)
                print(f"  源码已保存到: {filename}")
            except Exception as e:
                print(f"  保存源码失败: {e}")
            
            # 获取类的方法列表
            methods = get_methods_of_class(class_name)
            if methods:
                print(f"  方法数量: {len(methods)}")
                print(f"  主要方法: {', '.join(methods[:5])}")
                if len(methods) > 5:
                    print(f"  ... 还有 {len(methods) - 5} 个方法")
        else:
            print(f"✗ 无法获取 {class_name} 源码")
    
    print("\n2. 搜索消息发送相关的方法:")
    print("-" * 40)
    
    # 搜索关键方法
    key_methods = ["sendMessage", "send", "invoke", "d1", "J1", "e1", "n1", "setType", "getContent"]
    
    for method_name in key_methods:
        print(f"\n搜索方法: {method_name}")
        classes_with_method = search_method_by_name(method_name)
        if classes_with_method:
            print(f"  找到 {len(classes_with_method)} 个包含此方法的类:")
            for cls in classes_with_method[:10]:  # 只显示前10个
                print(f"    - {cls}")
            if len(classes_with_method) > 10:
                print(f"    ... 还有 {len(classes_with_method) - 10} 个类")
        else:
            print(f"  未找到包含方法 {method_name} 的类")

def analyze_pipeline_classes():
    """分析Pipeline相关的类"""
    print("\n3. 分析Pipeline处理架构:")
    print("-" * 40)
    
    # 搜索Pipeline相关的类
    pipeline_patterns = ["Pipeline", "SendMsg", "Processor"]
    
    for pattern in pipeline_patterns:
        print(f"\n搜索包含 '{pattern}' 的类:")
        matching_classes = search_classes_by_pattern(pattern)
        if matching_classes:
            print(f"  找到 {len(matching_classes)} 个相关类:")
            for cls in matching_classes[:10]:
                print(f"    - {cls}")
            if len(matching_classes) > 10:
                print(f"    ... 还有 {len(matching_classes) - 10} 个类")
        else:
            print(f"  未找到包含 '{pattern}' 的类")

def analyze_ui_classes():
    """分析UI相关的类"""
    print("\n4. 分析UI交互组件:")
    print("-" * 40)
    
    # 搜索UI相关的类
    ui_patterns = ["ChatFooter", "EditText", "Button", "onClick"]
    
    for pattern in ui_patterns:
        print(f"\n搜索包含 '{pattern}' 的类:")
        matching_classes = search_classes_by_pattern(pattern)
        if matching_classes:
            print(f"  找到 {len(matching_classes)} 个相关类:")
            for cls in matching_classes[:5]:
                print(f"    - {cls}")
            if len(matching_classes) > 5:
                print(f"    ... 还有 {len(matching_classes) - 5} 个类")
        else:
            print(f"  未找到包含 '{pattern}' 的类")

def main():
    """主函数"""
    print("开始分析微信消息发送流程...")
    
    # 测试JADX连接
    try:
        response = requests.get(f"{JADX_BASE_URL}/current-class", timeout=5)
        if response.status_code == 200:
            print("✓ JADX连接正常")
        else:
            print("✗ JADX连接异常")
            return
    except Exception as e:
        print(f"✗ 无法连接到JADX: {e}")
        return
    
    # 执行分析
    analyze_message_classes()
    analyze_pipeline_classes() 
    analyze_ui_classes()
    
    print("\n" + "=" * 60)
    print("分析完成！")
    print("关键源码文件已保存到当前目录")
    print("=" * 60)

if __name__ == "__main__":
    main()
