#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的微信消息发送流程分析脚本
"""

import requests
import json
import sys

# JADX HTTP API基础URL
JADX_BASE_URL = "http://127.0.0.1:8650"

def get_current_class():
    """获取当前打开的类"""
    try:
        response = requests.get(f"{JADX_BASE_URL}/current-class", timeout=10)
        response.raise_for_status()
        return json.loads(response.text)
    except Exception as e:
        print(f"获取当前类失败: {e}")
        return None

def get_class_source(class_name):
    """获取指定类的源码"""
    try:
        response = requests.get(f"{JADX_BASE_URL}/class-source", 
                              params={"class": class_name}, timeout=30)
        response.raise_for_status()
        return response.text
    except Exception as e:
        print(f"获取类 {class_name} 源码失败: {e}")
        return None

def search_method(method_name):
    """搜索方法"""
    try:
        response = requests.get(f"{JADX_BASE_URL}/search-method", 
                              params={"method": method_name}, timeout=30)
        response.raise_for_status()
        return response.text.splitlines()
    except Exception as e:
        print(f"搜索方法 {method_name} 失败: {e}")
        return []

def get_methods_of_class(class_name):
    """获取类的方法列表"""
    try:
        response = requests.get(f"{JADX_BASE_URL}/methods-of-class", 
                              params={"class": class_name}, timeout=30)
        response.raise_for_status()
        return response.text.splitlines()
    except Exception as e:
        print(f"获取类 {class_name} 方法列表失败: {e}")
        return []

def analyze_specific_classes():
    """分析特定的关键类"""
    print("=" * 60)
    print("微信消息发送流程分析 - 简化版")
    print("=" * 60)
    
    # 首先获取当前打开的类
    print("\n1. 获取当前打开的类:")
    print("-" * 40)
    current_class = get_current_class()
    if current_class:
        print(f"当前类名: {current_class.get('name', '未知')}")
        print(f"类型: {current_class.get('type', '未知')}")
        content = current_class.get('content', '')
        if content:
            print(f"源码长度: {len(content)} 字符")
            # 保存当前类源码
            with open("current_class.java", 'w', encoding='utf-8') as f:
                f.write(content)
            print("当前类源码已保存到 current_class.java")
            
            # 分析当前类是否包含消息相关的关键词
            keywords = ['send', 'message', 'msg', 'chat', 'text', 'content']
            found_keywords = []
            for keyword in keywords:
                if keyword.lower() in content.lower():
                    found_keywords.append(keyword)
            
            if found_keywords:
                print(f"发现相关关键词: {', '.join(found_keywords)}")
            else:
                print("当前类似乎不包含消息相关功能")
    else:
        print("无法获取当前类信息")
    
    # 尝试直接获取文档中提到的关键类
    print("\n2. 尝试获取文档中的关键类:")
    print("-" * 40)
    
    key_classes = [
        "qk.t7",      # 消息对象类
        "et0.o",      # 消息发送核心类  
        "vp4.z",      # 消息容器类
        "et0.e"       # 消息配置类
    ]
    
    for class_name in key_classes:
        print(f"\n尝试获取类: {class_name}")
        source = get_class_source(class_name)
        if source and len(source) > 100:  # 确保获取到了有效内容
            print(f"✓ 成功获取 {class_name} 源码 ({len(source)} 字符)")
            
            # 保存源码
            filename = f"wechat_{class_name.replace('.', '_')}.java"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(source)
            print(f"  源码已保存到: {filename}")
            
            # 获取方法列表
            methods = get_methods_of_class(class_name)
            if methods:
                print(f"  方法数量: {len(methods)}")
                print(f"  主要方法: {', '.join(methods[:5])}")
        else:
            print(f"✗ 无法获取 {class_name} 源码")
    
    # 搜索关键方法
    print("\n3. 搜索关键方法:")
    print("-" * 40)
    
    key_methods = ["d1", "J1", "e1", "setType", "getContent", "onClick"]
    
    for method_name in key_methods:
        print(f"\n搜索方法: {method_name}")
        classes = search_method(method_name)
        if classes:
            print(f"  找到 {len(classes)} 个包含此方法的类:")
            for cls in classes[:5]:  # 只显示前5个
                print(f"    - {cls}")
            if len(classes) > 5:
                print(f"    ... 还有 {len(classes) - 5} 个类")
        else:
            print(f"  未找到包含方法 {method_name} 的类")

def main():
    """主函数"""
    print("开始简化分析微信消息发送流程...")
    
    # 测试JADX连接
    try:
        response = requests.get(f"{JADX_BASE_URL}/current-class", timeout=5)
        if response.status_code == 200:
            print("✓ JADX连接正常")
        else:
            print("✗ JADX连接异常")
            return
    except Exception as e:
        print(f"✗ 无法连接到JADX: {e}")
        return
    
    # 执行分析
    analyze_specific_classes()
    
    print("\n" + "=" * 60)
    print("简化分析完成！")
    print("相关源码文件已保存到当前目录")
    print("=" * 60)

if __name__ == "__main__":
    main()
