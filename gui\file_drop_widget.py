#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件拖放组件
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QGroupBox, QProgressBar, QTextEdit,
                             QListWidget, QListWidgetItem, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMimeData
from PyQt5.QtGui import QDragEnterEvent, QDropEvent, QFont, QPalette

class FileTransferThread(QThread):
    """文件传输线程"""
    
    progress_updated = pyqtSignal(int)
    transfer_completed = pyqtSignal(bool, str)
    
    def __init__(self, adb_manager, device_serial, file_path, is_apk=False):
        super().__init__()
        self.adb_manager = adb_manager
        self.device_serial = device_serial
        self.file_path = file_path
        self.is_apk = is_apk
    
    def run(self):
        try:
            if self.is_apk:
                # 安装APK
                success = self.adb_manager.install_apk(self.device_serial, self.file_path)
                if success:
                    self.transfer_completed.emit(True, f"APK安装成功: {os.path.basename(self.file_path)}")
                else:
                    self.transfer_completed.emit(False, f"APK安装失败: {os.path.basename(self.file_path)}")
            else:
                # 推送文件
                success = self.adb_manager.push_file(self.device_serial, self.file_path)
                if success:
                    self.transfer_completed.emit(True, f"文件传输成功: {os.path.basename(self.file_path)}")
                else:
                    self.transfer_completed.emit(False, f"文件传输失败: {os.path.basename(self.file_path)}")
        except Exception as e:
            self.transfer_completed.emit(False, f"传输错误: {str(e)}")

class FileDropWidget(QWidget):
    """文件拖放组件"""
    
    # 添加文件拖放信号
    file_dropped = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.adb_manager = None
        self.current_device = None
        self.transfer_threads = []
        self.init_ui()
        
        # 启用拖放
        self.setAcceptDrops(True)
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        
        # 拖放区域
        drop_group = QGroupBox("文件拖放区域")
        drop_layout = QVBoxLayout()
        
        self.drop_area = QLabel()
        self.drop_area.setText("拖放文件到此处\n\n支持的文件类型:\n• APK文件 - 自动安装\n• 其他文件 - 传输到下载目录")
        self.drop_area.setAlignment(Qt.AlignCenter)
        self.drop_area.setFont(QFont("Arial", 12))
        self.drop_area.setMinimumHeight(150)
        self.drop_area.setStyleSheet("""
            QLabel {
                border: 3px dashed #cccccc;
                border-radius: 10px;
                background-color: #f9f9f9;
                color: #666666;
            }
        """)
        
        drop_layout.addWidget(self.drop_area)
        drop_group.setLayout(drop_layout)
        layout.addWidget(drop_group)
        
        # 文件操作按钮
        file_ops_group = QGroupBox("文件操作")
        file_ops_layout = QHBoxLayout()
        
        self.select_file_btn = QPushButton("选择文件")
        self.select_apk_btn = QPushButton("选择APK")
        self.open_download_btn = QPushButton("打开下载目录")
        
        self.select_file_btn.setEnabled(False)
        self.select_apk_btn.setEnabled(False)
        self.open_download_btn.setEnabled(False)
        
        file_ops_layout.addWidget(self.select_file_btn)
        file_ops_layout.addWidget(self.select_apk_btn)
        file_ops_layout.addWidget(self.open_download_btn)
        file_ops_layout.addStretch()
        
        file_ops_group.setLayout(file_ops_layout)
        layout.addWidget(file_ops_group)
        
        # 传输进度
        progress_group = QGroupBox("传输状态")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 9))
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_text)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # 连接信号
        self.select_file_btn.clicked.connect(self.select_file)
        self.select_apk_btn.clicked.connect(self.select_apk)
        self.open_download_btn.clicked.connect(self.open_download_folder)
        
        self.setLayout(layout)
    
    def set_adb_manager(self, adb_manager):
        """设置ADB管理器"""
        self.adb_manager = adb_manager
    
    def set_current_device(self, serial: str):
        """设置当前设备"""
        self.current_device = serial
        
        # 更新按钮状态
        enabled = serial is not None and self.adb_manager is not None
        self.select_file_btn.setEnabled(enabled)
        self.select_apk_btn.setEnabled(enabled)
        self.open_download_btn.setEnabled(enabled)
        
        # 更新拖放区域显示
        if enabled:
            self.drop_area.setStyleSheet("""
                QLabel {
                    border: 3px dashed #4CAF50;
                    border-radius: 10px;
                    background-color: #f0f8f0;
                    color: #2E7D32;
                }
            """)
        else:
            self.drop_area.setStyleSheet("""
                QLabel {
                    border: 3px dashed #cccccc;
                    border-radius: 10px;
                    background-color: #f9f9f9;
                    color: #666666;
                }
            """)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖入事件"""
        if event.mimeData().hasUrls() and self.current_device:
            event.acceptProposedAction()
            # 高亮显示拖放区域
            self.drop_area.setStyleSheet("""
                QLabel {
                    border: 3px dashed #2196F3;
                    border-radius: 10px;
                    background-color: #e3f2fd;
                    color: #1976D2;
                }
            """)
    
    def dragLeaveEvent(self, event):
        """拖出事件"""
        # 恢复正常显示
        if self.current_device:
            self.drop_area.setStyleSheet("""
                QLabel {
                    border: 3px dashed #4CAF50;
                    border-radius: 10px;
                    background-color: #f0f8f0;
                    color: #2E7D32;
                }
            """)
    
    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        if not self.current_device or not self.adb_manager:
            return
        
        # 恢复正常显示
        self.set_current_device(self.current_device)
        
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if os.path.isfile(file_path):
                files.append(file_path)
                # 发送文件拖放信号
                self.file_dropped.emit(file_path)
        
        if files:
            self.process_files(files)
    
    def select_file(self):
        """选择文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择要传输的文件", "", "所有文件 (*)")
        
        if files:
            for file_path in files:
                # 发送文件拖放信号
                self.file_dropped.emit(file_path)
            self.process_files(files)
    
    def select_apk(self):
        """选择APK文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择APK文件", "", "APK文件 (*.apk)")
        
        if files:
            for file_path in files:
                # 发送文件拖放信号
                self.file_dropped.emit(file_path)
            self.process_files(files)
    
    def process_files(self, file_paths):
        """处理文件列表"""
        if not self.current_device or not self.adb_manager:
            QMessageBox.warning(self, "警告", "请先选择设备")
            return
        
        for file_path in file_paths:
            self.transfer_file(file_path)
    
    def transfer_file(self, file_path):
        """传输单个文件"""
        filename = os.path.basename(file_path)
        is_apk = file_path.lower().endswith('.apk')
        
        # 显示传输状态
        if is_apk:
            self.log_message(f"开始安装APK: {filename}")
        else:
            self.log_message(f"开始传输文件: {filename}")
        
        # 创建传输线程
        transfer_thread = FileTransferThread(
            self.adb_manager, self.current_device, file_path, is_apk)
        
        transfer_thread.transfer_completed.connect(self.on_transfer_completed)
        transfer_thread.start()
        
        self.transfer_threads.append(transfer_thread)
    
    def on_transfer_completed(self, success, message):
        """传输完成回调"""
        self.log_message(message)
        
        if success:
            self.log_message("✓ 操作成功完成")
        else:
            self.log_message("✗ 操作失败")
    
    def log_message(self, message):
        """记录日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.status_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def open_download_folder(self):
        """打开设备下载目录"""
        if not self.current_device or not self.adb_manager:
            return
        
        try:
            # 列出下载目录的文件
            result = self.adb_manager.execute_command(self.current_device, "ls /sdcard/Download/")
            if result:
                self.log_message("下载目录文件列表:")
                for line in result.strip().split('\n'):
                    if line.strip():
                        self.log_message(f"  - {line.strip()}")
            else:
                self.log_message("下载目录为空或无法访问")
        except Exception as e:
            self.log_message(f"打开下载目录失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        # 等待所有传输线程完成
        for thread in self.transfer_threads:
            if thread.isRunning():
                thread.wait(timeout=3000) 