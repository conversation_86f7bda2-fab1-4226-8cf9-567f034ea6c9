/**
 * 微信类查找脚本
 * 用于分析当前微信版本的类结构，找到正确的Hook目标
 */

console.log("🔍 微信类结构分析脚本启动");

Java.perform(function() {
    console.log("[+] Java环境初始化完成");
    
    // 分析结果存储
    var analysisResults = {
        messageClasses: [],
        senderClasses: [],
        uiClasses: [],
        networkClasses: [],
        databaseClasses: []
    };
    
    console.log("\n[*] 开始分析微信类结构...");
    
    // 1. 查找消息相关类
    console.log("\n=== 1. 查找消息相关类 ===");
    Java.enumerateLoadedClasses({
        onMatch: function(className) {
            try {
                var clazz = Java.use(className);
                var fields = clazz.class.getDeclaredFields();
                var methods = clazz.class.getDeclaredMethods();
                
                // 检查是否包含消息相关字段
                var hasMessageFields = false;
                var messageFieldCount = 0;
                var fieldNames = [];
                
                for (var i = 0; i < fields.length; i++) {
                    var fieldName = fields[i].getName();
                    fieldNames.push(fieldName);
                    
                    if (fieldName.includes("content") || fieldName.includes("Content") ||
                        fieldName.includes("talker") || fieldName.includes("Talker") ||
                        fieldName.includes("createTime") || fieldName.includes("CreateTime") ||
                        fieldName.includes("msgId") || fieldName.includes("MsgId") ||
                        fieldName.includes("type") || fieldName.includes("Type")) {
                        messageFieldCount++;
                        hasMessageFields = true;
                    }
                }
                
                // 检查是否有消息相关方法
                var hasMessageMethods = false;
                var methodNames = [];
                
                for (var i = 0; i < methods.length; i++) {
                    var methodName = methods[i].getName();
                    methodNames.push(methodName);
                    
                    if (methodName === "setType" || methodName === "getContent" ||
                        methodName === "setContent" || methodName === "getTalker") {
                        hasMessageMethods = true;
                    }
                }
                
                // 如果是消息类，记录详细信息
                if (hasMessageFields && messageFieldCount >= 3) {
                    analysisResults.messageClasses.push({
                        className: className,
                        fieldCount: messageFieldCount,
                        hasSetType: methodNames.includes("setType"),
                        hasGetContent: methodNames.includes("getContent"),
                        fields: fieldNames.slice(0, 10), // 只显示前10个字段
                        methods: methodNames.slice(0, 10) // 只显示前10个方法
                    });
                }
                
            } catch (e) {
                // 忽略错误
            }
        },
        onComplete: function() {
            console.log(`[+] 找到 ${analysisResults.messageClasses.length} 个可能的消息类`);
            
            // 显示最有可能的消息类
            analysisResults.messageClasses.sort((a, b) => b.fieldCount - a.fieldCount);
            
            for (var i = 0; i < Math.min(5, analysisResults.messageClasses.length); i++) {
                var msgClass = analysisResults.messageClasses[i];
                console.log(`\n[${i+1}] ${msgClass.className}`);
                console.log(`    消息字段数: ${msgClass.fieldCount}`);
                console.log(`    有setType: ${msgClass.hasSetType}`);
                console.log(`    有getContent: ${msgClass.hasGetContent}`);
                console.log(`    字段示例: ${msgClass.fields.slice(0, 5).join(", ")}`);
                console.log(`    方法示例: ${msgClass.methods.slice(0, 5).join(", ")}`);
            }
        }
    });
    
    // 等待类枚举完成
    setTimeout(function() {
        // 2. 查找发送相关类
        console.log("\n=== 2. 查找发送相关类 ===");
        Java.enumerateLoadedClasses({
            onMatch: function(className) {
                if (className.includes("send") || className.includes("Send") ||
                    className.includes("msg") || className.includes("Msg")) {
                    try {
                        var clazz = Java.use(className);
                        var methods = clazz.class.getDeclaredMethods();
                        
                        for (var i = 0; i < methods.length; i++) {
                            var methodName = methods[i].getName();
                            if (methodName.includes("send") || methodName.includes("Send") ||
                                methodName.length <= 3) { // 短方法名可能是混淆后的
                                
                                analysisResults.senderClasses.push({
                                    className: className,
                                    methodName: methodName,
                                    paramCount: methods[i].getParameterTypes().length
                                });
                            }
                        }
                    } catch (e) {}
                }
            },
            onComplete: function() {
                console.log(`[+] 找到 ${analysisResults.senderClasses.length} 个可能的发送方法`);
                
                // 显示发送方法
                for (var i = 0; i < Math.min(10, analysisResults.senderClasses.length); i++) {
                    var sender = analysisResults.senderClasses[i];
                    console.log(`[${i+1}] ${sender.className}.${sender.methodName}(${sender.paramCount}参数)`);
                }
            }
        });
    }, 1000);
    
    // 3. 分析UI类
    setTimeout(function() {
        console.log("\n=== 3. 分析UI相关类 ===");
        
        try {
            var ChatFooterClass = Java.use("com.tencent.mm.pluginsdk.ui.chat.ChatFooter");
            var methods = ChatFooterClass.class.getDeclaredMethods();
            
            console.log("[+] ChatFooter类分析:");
            console.log(`    方法数量: ${methods.length}`);
            
            for (var i = 0; i < Math.min(10, methods.length); i++) {
                var method = methods[i];
                console.log(`    ${method.getName()}(${method.getParameterTypes().length}参数)`);
            }
            
        } catch (e) {
            console.log(`[-] ChatFooter类分析失败: ${e}`);
        }
        
        try {
            var EditTextClass = Java.use("com.tencent.mm.ui.widget.MMEditText");
            var methods = EditTextClass.class.getDeclaredMethods();
            
            console.log("\n[+] MMEditText类分析:");
            console.log(`    方法数量: ${methods.length}`);
            
            // 查找getText的重载
            var getTextOverloads = [];
            for (var i = 0; i < methods.length; i++) {
                if (methods[i].getName() === "getText") {
                    getTextOverloads.push(methods[i].getParameterTypes().length);
                }
            }
            console.log(`    getText重载: ${getTextOverloads.join(", ")}参数`);
            
        } catch (e) {
            console.log(`[-] MMEditText类分析失败: ${e}`);
        }
    }, 2000);
    
    // 4. 测试最有可能的消息类
    setTimeout(function() {
        console.log("\n=== 4. 测试消息类Hook ===");
        
        if (analysisResults.messageClasses.length > 0) {
            var bestClass = analysisResults.messageClasses[0];
            console.log(`[*] 测试Hook最佳候选类: ${bestClass.className}`);
            
            try {
                var TestClass = Java.use(bestClass.className);
                
                if (TestClass.setType) {
                    TestClass.setType.implementation = function(type) {
                        console.log(`\n🎯 [测试Hook成功] setType被调用: ${type}`);
                        return this.setType(type);
                    };
                    console.log("[✅] setType Hook测试成功");
                } else {
                    console.log("[❌] 该类没有setType方法");
                }
                
                // 测试其他可能的方法
                var methods = TestClass.class.getDeclaredMethods();
                for (var i = 0; i < methods.length; i++) {
                    var methodName = methods[i].getName();
                    if (methodName.length <= 3 && methodName.match(/^[a-zA-Z]\d*$/)) {
                        try {
                            var paramTypes = methods[i].getParameterTypes();
                            if (paramTypes.length === 1 && paramTypes[0].getName() === "java.lang.String") {
                                TestClass[methodName].implementation = function(str) {
                                    if (str && str.length > 0 && str.length < 500) {
                                        console.log(`🎯 [测试Hook] ${methodName}("${str}")`);
                                    }
                                    return this[methodName](str);
                                };
                                console.log(`[+] 测试Hook方法: ${methodName}(String)`);
                            }
                        } catch (e) {}
                    }
                }
                
            } catch (e) {
                console.log(`[❌] 测试Hook失败: ${e}`);
            }
        }
        
        console.log("\n" + "=".repeat(60));
        console.log("🎉 类结构分析完成！");
        console.log("📝 建议使用上述分析结果更新Hook脚本");
        console.log("🔍 现在可以发送消息来测试Hook效果");
        console.log("=".repeat(60));
        
    }, 3000);
});
