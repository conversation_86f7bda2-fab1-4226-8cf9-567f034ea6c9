# 微信消息发送Hook测试完整记录

## 项目概述

本项目通过JADX反编译微信APK，分析消息发送流程，并使用Frida Hook技术实现对微信消息发送过程的完整监控。

## 技术架构

### 1. 分析工具链
- **JADX反编译器**: 反编译微信APK获取Java源码
- **JADX MCP服务器**: 提供HTTP API接口查询反编译结果
- **Frida Hook框架**: 动态Hook微信运行时方法调用

### 2. 核心发现

#### 关键类映射表
| 混淆后类名 | 原始类名 | 功能描述 | Hook价值 |
|-----------|---------|----------|----------|
| `p713qk.C127133t7` | `qk.t7` | 消息对象类 | ⭐⭐⭐⭐⭐ |
| `et0.C77023o` | `et0.o` | 消息发送核心类 | ⭐⭐⭐⭐⭐ |
| `vp4.C149195z` | `vp4.z` | 消息容器类 | ⭐⭐⭐⭐ |
| `et0.C77024e` | `et0.e` | 消息配置类 | ⭐⭐⭐⭐ |

#### 重要方法映射表
| 混淆后方法名 | 原始方法名 | 功能描述 |
|-------------|-----------|----------|
| `m94407d1` | `d1` | 设置消息内容 |
| `m94391J1` | `J1` | 设置接收人ID |
| `m94408e1` | `e1` | 设置创建时间 |
| `m94412n1` | `n1` | 设置发送标识 |
| `mo62223i` | `i` | 核心发送方法 |

## Hook脚本开发过程

### 第一阶段：类名适配问题
**问题**: 初始使用混淆后的类名导致Hook失败
```
[-] Hook消息对象失败: Error: java.lang.ClassNotFoundException: Didn't find class "p713qk.C127133t7"
```

**解决方案**: 使用JADX注释中的原始类名
```javascript
// 错误的写法
var MessageClass = Java.use("p713qk.C127133t7");

// 正确的写法  
var MessageClass = Java.use("qk.t7");
```

### 第二阶段：方法重载问题
**问题**: EditText的getText方法有多个重载
```
[-] Hook MMEditText 失败: Error: getText(): has more than one overload
```

**解决方案**: 使用.overload()指定重载版本
```javascript
EditTextClass.getText.overload().implementation = function() { ... };
```

### 第三阶段：网络Hook兼容性
**问题**: 微信不使用标准OkHttp库
```
[-] Hook网络请求失败: Error: java.lang.ClassNotFoundException: Didn't find class "okhttp3.OkHttpClient"
```

**解决方案**: 移除网络Hook，专注于消息流程

## 完整消息发送流程分析

### 基于et0.o反编译代码的流程重构

```java
// et0.o.i方法的核心流程
public Object i(z messageContainer, b config, Continuation continuation) {
    // 1. 获取参数对象
    t1 t1Var = (t1) h().d("PPCKey_Params");
    f8 f8Var = config.b;  // 获取消息对象
    
    // 2. 设置消息属性（按顺序）
    f8Var.C1(1);           // 设置消息状态
    f8Var.J1(t1Var.b);     // 设置接收人
    f8Var.e1(timestamp);   // 设置创建时间
    f8Var.n1(1);           // 设置发送标识
    f8Var.d1(t1Var.d);     // 设置消息内容
    f8Var.setType(t1Var.e); // 设置消息类型
    
    // 3. 设置消息源
    String db = ((m3) n0.c(m3.class)).db(f8Var);
    if (db != null) {
        f8Var.g3(db);  // 设置消息源
    }
    
    // 4. 返回结果
    return new f(f8Var);
}
```

### Hook测试完整日志分析

#### 1. 脚本初始化阶段
```
🚀 微信消息发送Hook脚本启动
[+] Java环境初始化完成
[+] 成功找到消息类: qk.t7
[-] Hook消息源g3失败: TypeError: cannot set property 'implementation' of undefined
[+] 成功找到发送类: et0.o
[+] 成功找到ChatFooter类
```

**分析**: 
- ✅ 核心类Hook成功
- ❌ g3方法可能不存在或名称不同
- ✅ UI交互Hook正常

#### 2. 用户输入阶段
```
[消息内容设置] d1 被调用
[+] 消息内容: F
[+] 消息内容设置: D
[+] 消息内容设置: Fhh  
[+] 消息内容设置: Nihao
[+] 消息内容设置: [微笑]
[+] 消息内容设置: [撇嘴]
[+] 消息内容设置: Suhd
[+] 消息内容设置: Hhh
```

**分析**: 
- 捕获到用户实时输入过程
- 包括文本和表情符号
- 每次输入都会触发d1方法

#### 3. 发送按钮点击阶段
```
🎯 [UI交互] ChatFooter.A 被调用 (发送按钮点击)
[UI发送按钮点击] 调用堆栈:
com.tencent.mm.pluginsdk.ui.chat.ChatFooter.A(Native Method)
at com.tencent.mm.pluginsdk.ui.chat.v0.onClick(Unknown Source:280)
at android.view.View.performClick(View.java:7506)
```

**分析**:
- ✅ 成功捕获发送按钮点击事件
- ✅ 完整的UI事件调用堆栈
- 触发后续的消息发送流程

#### 4. 核心发送方法调用阶段
```
🚀 [核心发送方法] et0.o.i 被调用
[+] 消息容器(vp4.z): vp4.z@3a23436
[+] 配置对象(et0.e): et0.e@11726ae
[+] 协程回调: [object Object]
```

**分析**:
- ✅ 成功Hook核心发送方法
- ✅ 获取到消息容器和配置对象
- ✅ 使用Kotlin协程异步处理

#### 5. 消息对象构造阶段
```
[消息状态设置] C1 被调用 → 状态: 1
[接收人设置] J1 被调用 → 接收人: wxid_uisi5rto449h22  
[创建时间设置] e1 被调用 → 时间: 1752678590078 (07/16/2025, 11:09:50 PM)
[发送标识设置] n1 被调用 → 是否发送: 1 (发送)
[消息内容设置] d1 被调用 → 内容: Df
[消息类型设置] setType 被调用 → 类型: 1 (文本消息)
```

**分析**:
- ✅ 完整捕获消息对象构造过程
- ✅ 属性设置顺序与反编译代码一致
- ✅ 时间戳、接收人、内容等关键信息完整

#### 6. 发送完成阶段
```
[+] 原始发送方法调用完成
[+] 返回结果类型: et0.f
[消息状态设置] C1 被调用 → 状态: 2
```

**分析**:
- ✅ 发送方法正常返回
- ✅ 返回et0.f类型对象
- ✅ 消息状态更新为2（可能表示已发送）

## 技术要点总结

### 1. 类名使用原则
- **使用原始类名**: 基于JADX注释 `/* renamed from: qk.t7 */`
- **避免混淆类名**: 如 `p713qk.C127133t7` 等

### 2. Hook时机分析
- **输入阶段**: 实时捕获用户输入（d1方法）
- **UI交互**: 发送按钮点击（ChatFooter.A）
- **核心处理**: 消息发送逻辑（et0.o.i）
- **对象构造**: 消息属性设置（C1, J1, e1, n1, setType）

### 3. 调用堆栈特征
```
UI点击 → ChatFooter.A → et0.o.i → 消息对象构造 → 发送完成
```

### 4. 协程处理机制
- 使用Kotlin协程实现异步处理
- 避免阻塞UI线程
- 通过Continuation回调处理结果

## 安全研究价值

### 1. 消息拦截点
- **内容级别**: d1方法可拦截和修改消息内容
- **接收人级别**: J1方法可修改消息接收者
- **类型级别**: setType可改变消息类型

### 2. 时序分析
- 用户输入 → 实时内容更新 → 点击发送 → 核心处理 → 对象构造 → 发送完成
- 整个流程耗时约几十毫秒

### 3. 数据流向
```
用户输入 → EditText → d1(content) → et0.o.i → 消息对象 → 网络发送
```

## 后续研究方向

### 1. 消息源Hook
- 研究g3方法的正确Hook方式
- 分析消息源的作用和格式

### 2. 网络层分析
- 找到微信实际使用的网络库
- Hook网络请求查看消息传输格式

### 3. 数据库层Hook
- Hook消息存储过程
- 分析本地消息数据库结构

### 4. 其他消息类型
- 图片消息发送流程
- 语音消息发送流程
- 文件传输流程

## 结论

通过本次Hook测试，成功实现了对微信消息发送完整流程的监控，验证了基于JADX反编译代码分析的准确性。Hook脚本能够：

1. ✅ **完整捕获消息发送流程**: 从用户输入到发送完成
2. ✅ **准确获取消息详情**: 内容、接收人、时间、类型等
3. ✅ **实时监控用户操作**: UI交互和输入过程
4. ✅ **提供调用堆栈信息**: 便于深度分析

这为微信安全研究、逆向工程和Hook开发提供了完整的技术基础。
