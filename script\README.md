# 微信消息发送Hook脚本使用说明

## 脚本概述

`hook_msg.js` 是一个完整的微信消息发送流程Hook脚本，基于JADX反编译代码分析开发，能够监控和记录微信发送消息的完整过程。

## 功能特性

### 🎯 核心Hook点

1. **消息对象Hook** (`qk.t7` / `p713qk.C127133t7`)
   - 消息内容设置 (`m94407d1`)
   - 接收人设置 (`m94391J1`) 
   - 创建时间设置 (`m94408e1`)
   - 消息类型设置 (`setType`)
   - 发送标识设置 (`m94412n1`)
   - 消息内容获取 (`getContent`)
   - 创建时间获取 (`getCreateTime`)

2. **消息发送核心Hook** (`et0.o` / `et0.C77023o`)
   - 核心发送方法 (`mo62223i`)
   - 完整的参数和返回值监控

3. **Pipeline处理Hook**
   - `np4.q` Pipeline组件
   - `vp4.c0` Pipeline组件
   - 流水线处理过程监控

4. **UI交互Hook**
   - ChatFooter发送按钮点击 (`ChatFooter.A`)
   - 输入框内容获取 (`MMEditText.getText`)

5. **数据库操作Hook**
   - 消息对象转数据库格式 (`convertTo`)
   - ContentValues字段监控

6. **网络请求Hook**
   - OkHttp请求拦截
   - 消息相关网络请求监控

7. **统计监控**
   - 实时消息统计
   - 按类型分类统计
   - 定期报告功能

### 📊 监控信息

- **消息内容**: 完整的消息文本内容
- **接收人信息**: 微信ID和用户信息
- **时间戳**: 消息创建和发送时间
- **消息类型**: 文本、图片、语音、视频等
- **调用堆栈**: 完整的方法调用链
- **网络请求**: 相关的HTTP请求
- **数据库操作**: 消息存储过程

## 使用方法

### 1. 环境准备

确保已安装：
- Frida框架
- 已Root的Android设备或模拟器
- 微信应用

### 2. 运行脚本

```bash
# 方法1: 直接运行
frida -U -f com.tencent.mm -l script/hook_msg.js --no-pause

# 方法2: 附加到运行中的微信
frida -U com.tencent.mm -l script/hook_msg.js

# 方法3: 使用spawn模式
frida -U -f com.tencent.mm -l script/hook_msg.js
```

### 3. 查看输出

脚本运行后会显示：
```
============================================================
微信消息发送Hook脚本启动
============================================================
[+] Java环境初始化完成
[*] 开始Hook消息对象类 qk.t7...
[+] 成功找到消息类: p713qk.C127133t7
...
🎉 微信消息发送Hook脚本初始化完成！
```

### 4. 测试Hook

1. 打开微信应用
2. 进入任意聊天界面
3. 输入消息并点击发送
4. 观察Frida控制台输出

## 输出示例

### 消息发送过程输出

```
==================================================
[消息内容设置] m94407d1 被调用
[+] 消息内容: Hello World
[+] 内容长度: 11 字符
[+] 消息对象: p713qk.C127133t7@12345678

==================================================
[接收人设置] m94391J1 被调用
[+] 接收人ID: wxid_uisi5rto449h22
[+] 消息对象: p713qk.C127133t7@12345678

==================================================
[消息类型设置] setType 被调用
[+] 消息类型: 1 (文本消息)
[+] 消息对象: p713qk.C127133t7@12345678

============================================================
🚀 [核心发送方法] mo62223i 被调用
============================================================
[+] 消息容器: vp4.C149195z@87654321
[+] 配置对象: et0.C77024e@11111111
[+] 协程回调: kotlin.coroutines.Continuation@22222222
```

### 统计信息输出

```
========================================
📊 消息发送统计信息
========================================
总消息数: 15
文本消息: 12
图片消息: 2
语音消息: 1
视频消息: 0
其他消息: 0
运行时间: 120秒
========================================
```

## 自定义配置

### 修改Hook范围

可以通过注释相关代码块来禁用特定的Hook功能：

```javascript
// 禁用UI Hook
/*
try {
    // UI交互Hook代码
} catch (e) {
    console.log(`[-] Hook UI交互失败: ${e}`);
}
*/
```

### 修改输出格式

可以自定义日志输出格式：

```javascript
// 修改消息内容Hook的输出
MessageClass.m94407d1.implementation = function(content) {
    console.log(`[自定义] 消息: ${content}`);
    return this.m94407d1(content);
};
```

### 添加过滤条件

可以添加条件来过滤特定的消息：

```javascript
// 只Hook包含特定关键词的消息
MessageClass.m94407d1.implementation = function(content) {
    if (content && content.includes("关键词")) {
        console.log(`[过滤] 发现关键词消息: ${content}`);
    }
    return this.m94407d1(content);
};
```

## 注意事项

1. **兼容性**: 脚本基于特定版本的微信开发，不同版本可能需要调整类名和方法名
2. **性能**: Hook会影响应用性能，建议在测试环境使用
3. **稳定性**: 过度Hook可能导致应用崩溃，建议逐步启用功能
4. **隐私**: 请遵守相关法律法规，仅在授权环境下使用

## 故障排除

### 常见错误

1. **类未找到**: 检查微信版本和类名是否匹配
2. **方法未找到**: 验证方法签名是否正确
3. **权限错误**: 确保设备已Root且Frida有足够权限

### 调试技巧

1. 使用 `Java.enumerateLoadedClasses()` 查找正确的类名
2. 使用 `Java.use("类名").class.getDeclaredMethods()` 查看可用方法
3. 逐步启用Hook功能，定位问题所在

## 扩展开发

脚本采用模块化设计，可以轻松添加新的Hook点：

```javascript
try {
    // 新的Hook模块
    console.log("\n[*] 开始Hook新功能...");
    
    var NewClass = Java.use("新类名");
    NewClass.新方法.implementation = function() {
        console.log("[新功能] 被调用");
        return this.新方法.apply(this, arguments);
    };
    
    console.log("[+] 新功能Hook设置完成");
} catch (e) {
    console.log(`[-] Hook新功能失败: ${e}`);
}
```

## 版本历史

- v1.0: 初始版本，支持基础消息发送Hook
- 基于JADX反编译代码分析开发
- 支持微信最新版本的消息发送流程监控
