# 微信主动发送消息API使用说明

## 概述

基于对微信消息发送流程的深度分析，我们开发了主动发送消息的API。该API可以在不依赖UI操作的情况下，直接调用微信内部方法发送消息。

## 文件说明

### 1. `hook_msg.js` - 完整Hook脚本
- 包含消息监控和主动发送功能
- 适合需要同时监控和发送的场景

### 2. `send_message_api.js` - 专用发送API
- 专门用于主动发送消息
- 更轻量，专注于发送功能

## API函数说明

### 核心函数

#### 1. `SendMessage(wxid, content, type)`
**完整版发送函数**

**参数**:
- `wxid` (string): 接收人微信ID或群ID
- `content` (string): 消息内容
- `type` (number): 消息类型，可选，默认为1

**消息类型**:
- `1`: 文本消息 (默认)
- `3`: 图片消息
- `34`: 语音消息
- `43`: 视频消息
- `47`: 表情消息
- `48`: 位置消息
- `49`: 链接消息

**返回值**: `boolean` - 发送是否成功

**示例**:
```javascript
// 发送文本消息
SendMessage('wxid_abc123', 'Hello World', 1);

// 发送表情消息
SendMessage('wxid_abc123', '[微笑]', 47);
```

#### 2. `SendMsg(wxid, content)`
**简化版发送函数**

**参数**:
- `wxid` (string): 接收人微信ID
- `content` (string): 消息内容

**返回值**: `boolean` - 发送是否成功

**示例**:
```javascript
SendMsg('wxid_abc123', 'Hello');
```

#### 3. `SendToCurrentChat(content)`
**发送到当前聊天**

**参数**:
- `content` (string): 消息内容

**返回值**: `boolean` - 发送是否成功

**示例**:
```javascript
SendToCurrentChat('Hello from Frida!');
```

#### 4. `getCurrentChatWxid()`
**获取当前聊天对象ID**

**返回值**: `string` - 当前聊天对象的wxid

**示例**:
```javascript
var currentWxid = getCurrentChatWxid();
console.log('当前聊天对象:', currentWxid);
```

## 使用方法

### 方法1: 使用完整Hook脚本
```bash
# 加载包含发送功能的完整Hook脚本
frida -U com.tencent.mm -l script/hook_msg.js

# 在Frida控制台中调用
SendMessage('wxid_xxx', 'Hello World');
```

### 方法2: 使用专用发送API
```bash
# 加载专用发送API
frida -U com.tencent.mm -l script/send_message_api.js

# 在Frida控制台中调用
SendMsg('wxid_xxx', 'Hello');
```

### 方法3: 组合使用
```bash
# 先加载Hook脚本监控消息
frida -U com.tencent.mm -l script/hook_msg.js

# 然后在另一个终端加载发送API
frida -U com.tencent.mm -l script/send_message_api.js
```

## 技术实现原理

### 1. 消息对象构造
```javascript
// 创建消息对象
var MessageClass = Java.use("qk.t7");
var messageObj = MessageClass.$new();

// 设置消息属性 (按Hook观察到的顺序)
messageObj.C1(1);                    // 设置状态
messageObj.J1(wxid);                 // 设置接收人
messageObj.e1(currentTime);          // 设置创建时间
messageObj.n1(1);                    // 设置发送标识
messageObj.d1(content);              // 设置消息内容
messageObj.setType(type);            // 设置消息类型
```

### 2. 发送器调用
```javascript
// 获取现有发送器实例
Java.choose("et0.o", {
    onMatch: function(instance) {
        senderInstance = instance;
        return "stop";
    }
});

// 调用核心发送方法
var result = senderInstance.i(containerObj, configObj, continuation);
```

### 3. 协程处理
```javascript
// 创建协程回调
var ContinuationImpl = Java.registerClass({
    name: 'com.frida.ContinuationImpl',
    implements: [Java.use('kotlin.coroutines.Continuation')],
    methods: {
        resumeWith: function(result) {
            console.log("消息发送完成");
        }
    }
});
```

## 使用示例

### 基础使用
```javascript
// 发送简单文本消息
SendMsg('wxid_abc123', 'Hello World');

// 发送带表情的消息
SendMessage('wxid_abc123', '你好[微笑]', 1);

// 发送到群聊
SendMsg('123456789@chatroom', '大家好！');
```

### 批量发送
```javascript
// 批量发送消息
var contacts = ['wxid_1', 'wxid_2', 'wxid_3'];
var message = 'Hello from Frida!';

contacts.forEach(function(wxid) {
    setTimeout(function() {
        SendMsg(wxid, message);
    }, 1000); // 间隔1秒发送
});
```

### 自动回复
```javascript
// 结合Hook实现自动回复
// 在hook_msg.js的消息内容Hook中添加:
MessageClass.d1.implementation = function(content) {
    var result = this.d1(content);
    
    // 检测到特定消息时自动回复
    if (content === 'ping') {
        setTimeout(function() {
            SendMsg('发送者wxid', 'pong');
        }, 500);
    }
    
    return result;
};
```

## 注意事项

### 1. 安全性
- ⚠️ 仅在授权环境下使用
- ⚠️ 不要用于垃圾消息发送
- ⚠️ 遵守相关法律法规

### 2. 稳定性
- 📱 不同微信版本可能需要调整类名
- 🔄 发送频率过高可能导致限制
- 💾 建议添加发送间隔避免被检测

### 3. 兼容性
- ✅ 基于当前微信版本开发
- ⚠️ 新版本可能需要更新代码
- 🔍 建议先测试再正式使用

### 4. 调试技巧
```javascript
// 开启详细日志
console.log("发送前状态检查...");

// 检查消息对象
console.log("消息对象:", messageObj);
console.log("接收人:", messageObj.getTalker());
console.log("内容:", messageObj.getContent());

// 发送后验证
setTimeout(function() {
    console.log("发送状态检查...");
}, 1000);
```

## 故障排除

### 常见问题

1. **类找不到错误**
   ```
   Error: java.lang.ClassNotFoundException: qk.t7
   ```
   **解决**: 检查微信版本，可能需要更新类名

2. **方法调用失败**
   ```
   TypeError: cannot call method 'i' of undefined
   ```
   **解决**: 确保获取到了有效的发送器实例

3. **协程回调错误**
   ```
   Error: Continuation implementation error
   ```
   **解决**: 检查协程回调的实现

### 调试命令
```javascript
// 检查类是否存在
Java.use("qk.t7");

// 查找现有实例
Java.choose("et0.o", {
    onMatch: function(instance) {
        console.log("找到实例:", instance);
    }
});

// 测试消息对象创建
var msg = Java.use("qk.t7").$new();
console.log("消息对象:", msg);
```

## 扩展功能

### 1. 消息队列
```javascript
var messageQueue = [];

function addToQueue(wxid, content) {
    messageQueue.push({wxid: wxid, content: content});
}

function processQueue() {
    if (messageQueue.length > 0) {
        var msg = messageQueue.shift();
        SendMsg(msg.wxid, msg.content);
        setTimeout(processQueue, 2000); // 2秒间隔
    }
}
```

### 2. 消息模板
```javascript
var templates = {
    greeting: "你好，{name}！",
    goodbye: "再见，{name}！"
};

function sendTemplate(wxid, templateName, params) {
    var template = templates[templateName];
    var content = template.replace(/{(\w+)}/g, function(match, key) {
        return params[key] || match;
    });
    return SendMsg(wxid, content);
}

// 使用示例
sendTemplate('wxid_abc', 'greeting', {name: '张三'});
```

## 结论

通过深入分析微信的消息发送机制，我们成功实现了主动发送消息的API。这个API不仅展示了逆向工程的技术价值，也为自动化测试、消息机器人等应用提供了技术基础。

在使用时请务必遵守相关法律法规，仅在授权环境下进行技术研究和测试。
