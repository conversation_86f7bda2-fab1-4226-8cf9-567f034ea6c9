#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立屏幕窗口
提供设备屏幕镜像、触控操作和文件拖放功能
"""

import os
import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QGroupBox, QFrame, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPoint, QMetaObject, QSize
from PyQt5.QtGui import QPixmap, QImage, QPainter, QPen, QFont, QDragEnterEvent, QDropEvent
from PIL import Image, ImageQt
import time
import io
import numpy as np

class ScreenWindow(QWidget):
    """独立屏幕窗口"""
    
    # 定义信号
    screen_clicked = pyqtSignal(int, int)  # 屏幕点击信号
    screen_swiped = pyqtSignal(int, int, int, int)  # 屏幕滑动信号
    file_dropped = pyqtSignal(str)  # 文件拖放信号
    
    def __init__(self):
        super().__init__()
        self.adb_manager = None
        self.current_device = None
        
        # 鼠标操作相关
        self.mouse_pressed = False
        self.press_pos = QPoint()
        self.device_width = 1080
        self.device_height = 1920
        self.scale_x = 1.0
        self.scale_y = 1.0
        
        # 缓存上一帧
        self.last_pixmap = None
        
        self.init_ui()
        
        # 启用拖放
        self.setAcceptDrops(True)
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("设备屏幕镜像")
        self.setFixedSize(450, 800)  # 适配竖屏手机的尺寸
        
        # 主布局
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 屏幕显示区域
        self.screen_label = QLabel()
        self.screen_label.setMinimumSize(440, 730)
        self.screen_label.setScaledContents(True)
        self.screen_label.setStyleSheet("""
            QLabel {
                border: 2px solid #333333;
                background-color: #000000;
                border-radius: 10px;
            }
        """)
        
        # 设置默认显示
        self.screen_label.setText("设备屏幕镜像\n\n请在主窗口连接设备\n并开始屏幕镜像")
        self.screen_label.setAlignment(Qt.AlignCenter)
        self.screen_label.setFont(QFont("Arial", 12))
        self.screen_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #666666;
                background-color: #1a1a1a;
                color: #cccccc;
                border-radius: 10px;
            }
        """)
        
        layout.addWidget(self.screen_label)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        self.screenshot_btn = QPushButton("截图")
        self.screenshot_btn.setEnabled(False)
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        
        # 添加FPS显示
        self.fps_label = QLabel("FPS: --")
        self.fps_label.setStyleSheet("color: #4CAF50; font-size: 12px;")
        self.last_update_time = time.time()
        self.frame_count = 0
        self.fps_update_interval = 1.0  # 每秒更新一次FPS
        self.last_fps_update = time.time()
        
        control_layout.addWidget(self.screenshot_btn)
        control_layout.addWidget(self.fps_label)
        control_layout.addStretch()
        
        # 状态标签
        self.status_label = QLabel("未连接设备")
        self.status_label.setStyleSheet("color: #666666; font-size: 12px;")
        control_layout.addWidget(self.status_label)
        
        layout.addLayout(control_layout)
        
        self.setLayout(layout)
        
        # 设置窗口属性
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        self.setAttribute(Qt.WA_DeleteOnClose, False)  # 关闭时不删除窗口
        
    def set_adb_manager(self, adb_manager):
        """设置ADB管理器"""
        self.adb_manager = adb_manager
        
    def set_current_device(self, serial: str):
        """设置当前设备"""
        self.current_device = serial
        
        if serial:
            self.status_label.setText(f"已连接: {serial}")
            self.screenshot_btn.setEnabled(True)
            
            # 更新屏幕显示提示
            self.screen_label.setText("设备已连接\n\n请在主窗口点击\n'开始屏幕镜像'")
            self.screen_label.setStyleSheet("""
                QLabel {
                    border: 2px dashed #4CAF50;
                    background-color: #0d1a0d;
                    color: #4CAF50;
                    border-radius: 10px;
                }
            """)
        else:
            self.status_label.setText("未连接设备")
            self.screenshot_btn.setEnabled(False)
            
            # 重置屏幕显示
            self.screen_label.setText("设备屏幕镜像\n\n请在主窗口连接设备\n并开始屏幕镜像")
            self.screen_label.setStyleSheet("""
                QLabel {
                    border: 2px dashed #666666;
                    background-color: #1a1a1a;
                    color: #cccccc;
                    border-radius: 10px;
                }
            """)
            
    def update_screen(self, image: Image.Image):
        """更新屏幕显示"""
        try:
            if image and not self.isHidden():
                # 获取设备实际尺寸
                self.device_width, self.device_height = image.size
                
                # 更新FPS计数
                self.frame_count += 1
                current_time = time.time()
                
                # 每秒更新一次FPS显示
                if current_time - self.last_fps_update >= self.fps_update_interval:
                    fps = self.frame_count / (current_time - self.last_fps_update)
                    self.fps_label.setText(f"FPS: {fps:.1f}")
                    self.frame_count = 0
                    self.last_fps_update = current_time
                
                # 高效转换图像
                if QApplication.instance().thread() == self.thread():
                    self._fast_update_pixmap(image)
                else:
                    # 如果在不同线程，使用信号槽
                    QMetaObject.invokeMethod(self, "_fast_update_pixmap", 
                                           Qt.QueuedConnection,
                                           Q_ARG(object, image))
                    
        except Exception as e:
            print(f"更新屏幕显示失败: {e}")
    
    def _fast_update_pixmap(self, image):
        """高效的图像转换和显示方法"""
        try:
            # 将PIL图像转换为RGB格式（如果不是的话）
            if image.mode != 'RGB':
                rgb_image = image.convert('RGB')
            else:
                rgb_image = image
            
            # 获取图像数据
            width, height = rgb_image.size
            
            # 使用numpy加速转换
            img_array = np.array(rgb_image)
            bytes_per_line = 3 * width
            
            # 直接创建QImage，避免中间转换
            qimage = QImage(img_array.data, width, height, bytes_per_line, QImage.Format_RGB888)
            
            if not qimage.isNull():
                # 从QImage创建QPixmap
                pixmap = QPixmap.fromImage(qimage)
                
                if not pixmap.isNull():
                    # 计算缩放比例
                    label_size = self.screen_label.size()
                    if label_size.width() > 0 and label_size.height() > 0:
                        self.scale_x = self.device_width / label_size.width()
                        self.scale_y = self.device_height / label_size.height()
                    
                    # 设置图像
                    self.screen_label.setPixmap(pixmap)
                    self.last_pixmap = pixmap
                    
                    # 更新样式为正常显示
                    self.screen_label.setStyleSheet("""
                        QLabel {
                            border: 2px solid #333333;
                            background-color: #000000;
                            border-radius: 10px;
                        }
                    """)
                    
        except Exception as e:
            print(f"快速更新屏幕显示失败: {e}")
            
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and self.current_device:
            # 检查点击是否在屏幕区域内
            if self.screen_label.geometry().contains(event.pos()):
                self.mouse_pressed = True
                # 转换为相对于屏幕标签的坐标
                relative_pos = event.pos() - self.screen_label.pos()
                self.press_pos = relative_pos
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.mouse_pressed and self.current_device:
            # 转换为相对于屏幕标签的坐标
            relative_pos = event.pos() - self.screen_label.pos()
            release_pos = relative_pos
            
            # 检查释放位置是否在屏幕区域内
            if self.screen_label.geometry().contains(event.pos()):
                # 计算设备坐标
                device_x = int(release_pos.x() * self.scale_x)
                device_y = int(release_pos.y() * self.scale_y)
                
                # 判断是点击还是滑动
                distance = ((release_pos.x() - self.press_pos.x()) ** 2 + 
                           (release_pos.y() - self.press_pos.y()) ** 2) ** 0.5
                
                if distance < 10:  # 点击
                    self.screen_clicked.emit(device_x, device_y)
                else:  # 滑动
                    press_device_x = int(self.press_pos.x() * self.scale_x)
                    press_device_y = int(self.press_pos.y() * self.scale_y)
                    self.screen_swiped.emit(press_device_x, press_device_y, device_x, device_y)
            
            self.mouse_pressed = False
            
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖入事件"""
        if event.mimeData().hasUrls() and self.current_device:
            event.acceptProposedAction()
            # 高亮显示拖放区域
            self.screen_label.setStyleSheet("""
                QLabel {
                    border: 3px dashed #2196F3;
                    background-color: #0d1a2e;
                    border-radius: 10px;
                }
            """)
    
    def dragLeaveEvent(self, event):
        """拖出事件"""
        # 恢复正常显示
        if self.current_device:
            self.screen_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #333333;
                    background-color: #000000;
                    border-radius: 10px;
                }
            """)
    
    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        if not self.current_device:
            return
        
        # 恢复正常显示
        self.screen_label.setStyleSheet("""
            QLabel {
                border: 2px solid #333333;
                background-color: #000000;
                border-radius: 10px;
            }
        """)
        
        # 处理拖放的文件
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if os.path.isfile(file_path):
                files.append(file_path)
        
        # 处理每个文件
        for file_path in files:
            self.file_dropped.emit(file_path)
            
    def take_screenshot(self):
        """截图"""
        if not self.current_device or not self.adb_manager:
            return
        
        try:
            image = self.adb_manager.capture_screen(self.current_device)
            if image:
                # 保存截图
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"
                image.save(filename)
                self.status_label.setText(f"截图已保存: {filename}")
                
                # 3秒后恢复状态显示
                QTimer.singleShot(3000, lambda: self.status_label.setText(f"已连接: {self.current_device}"))
        except Exception as e:
            self.status_label.setText(f"截图失败: {e}")
            QTimer.singleShot(3000, lambda: self.status_label.setText(f"已连接: {self.current_device}"))
            
    def closeEvent(self, event):
        """关闭事件"""
        # 隐藏窗口而不是关闭
        self.hide()
        event.ignore() 