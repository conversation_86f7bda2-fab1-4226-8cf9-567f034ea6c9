@echo off
chcp 65001 >nul
echo ============================================================
echo 微信消息发送Hook脚本启动器
echo ============================================================
echo.

:menu
echo 请选择要运行的Hook脚本:
echo.
echo [1] 完整版Hook脚本 (hook_msg.js)
echo     - 监控完整的消息发送流程
echo     - 包含调用堆栈、网络请求、数据库操作等
echo     - 适合深度分析
echo.
echo [2] 简化版Hook脚本 (hook_msg_simple.js)  
echo     - 只监控核心消息发送过程
echo     - 输出简洁，性能影响小
echo     - 适合快速测试
echo.
echo [3] 检查Frida和设备连接状态
echo.
echo [4] 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto full_hook
if "%choice%"=="2" goto simple_hook  
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:full_hook
echo.
echo 启动完整版Hook脚本...
echo ============================================================
echo 注意: 完整版脚本会产生大量日志输出
echo 建议将输出重定向到文件: 
echo frida -U com.tencent.mm -l hook_msg.js ^> output.log
echo ============================================================
echo.
pause
frida -U com.tencent.mm -l hook_msg.js
goto menu

:simple_hook
echo.
echo 启动简化版Hook脚本...
echo ============================================================
echo 简化版脚本只显示关键信息，适合实时监控
echo ============================================================
echo.
pause
frida -U com.tencent.mm -l hook_msg_simple.js
goto menu

:check_status
echo.
echo 检查Frida和设备状态...
echo ============================================================
echo.
echo 1. 检查Frida版本:
frida --version
echo.
echo 2. 检查连接的设备:
frida-ls-devices
echo.
echo 3. 检查微信进程:
frida-ps -U | findstr tencent
echo.
echo ============================================================
pause
goto menu

:exit
echo.
echo 感谢使用微信消息Hook脚本！
echo.
pause
exit
