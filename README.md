# ADB工具 - 设备管理与日志查看

一个功能完善的Python ADB工具，提供图形化界面用于Android设备管理和日志查看。

## 功能特性

### 设备管理
- 📱 自动检测USB连接的Android设备
- 🔍 扫描WiFi网络中的ADB设备
- 🔗 支持WiFi连接和断开设备
- 📊 显示设备详细信息（品牌、型号、Android版本等）
- 🔄 实时刷新设备状态

### 日志查看
- 📋 内置Logcat日志查看器，支持彩色显示
- 🔍 支持按标签和级别过滤日志
- 💾 支持保存日志到文件
- 🎨 根据日志级别自动着色（错误、警告、信息等）
- 📜 自动滚动和行数限制

### Android Studio集成
- 🚀 一键启动Android Studio的Logcat窗口
- 🔧 自动检测Android Studio安装路径
- 📱 直接传递设备序列号给Android Studio

### 用户界面
- 🖥️ 现代化的GUI界面，基于PyQt5
- 📊 设备列表表格显示
- 🔄 实时状态更新
- 🎯 直观的操作按钮和菜单

## 安装要求

### 系统要求
- Windows 10/11
- Python 3.7+
- Android SDK (ADB工具)
- Android Studio (可选，用于集成功能)

### Python依赖
- PyQt5 - GUI框架
- ppadb - Python ADB客户端
- pure-python-adb - 纯Python ADB实现
- requests - HTTP请求库
- psutil - 系统进程管理
- netifaces - 网络接口信息

## 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd android-adb-tool
   ```

2. **运行安装脚本**
   ```bash
   # Windows
   install.bat
   
   # 或手动安装
   pip install -r requirements.txt
   ```

3. **启动程序**
   ```bash
   # Windows
   run.bat
   
   # 或手动启动
   python main.py
   ```

## 使用说明

### 设备连接

1. **USB连接**
   - 使用USB数据线连接Android设备
   - 确保设备已开启"USB调试"
   - 程序会自动检测设备

2. **WiFi连接**
   - 确保设备和电脑在同一网络
   - 在设备上启用"无线调试"
   - 点击"扫描WiFi"按钮搜索设备
   - 或手动输入IP地址和端口连接

### 日志查看

1. **启动Logcat**
   - 选择已连接的设备
   - 点击"开始Logcat"按钮
   - 日志将实时显示在右侧面板

2. **过滤日志**
   - 在"标签"输入框中输入要过滤的标签
   - 在"级别"下拉框中选择日志级别
   - 重新启动Logcat应用过滤器

3. **保存日志**
   - 点击"保存日志"按钮
   - 选择保存位置和文件名
   - 日志将以文本格式保存

### Android Studio集成

1. **启动Android Studio Logcat**
   - 选择已连接的设备
   - 点击"启动Android Studio Logcat"按钮
   - 程序会自动启动Android Studio并打开Logcat窗口

## 项目结构

```
android-adb-tool/
├── main.py              # 主程序入口
├── requirements.txt     # Python依赖
├── install.bat         # Windows安装脚本
├── run.bat            # Windows启动脚本
├── README.md          # 项目说明
├── gui/               # GUI界面模块
│   ├── __init__.py
│   └── main_window.py # 主窗口类
└── utils/             # 工具模块
    ├── __init__.py
    └── adb_manager.py # ADB管理器
```

## 功能截图

### 主界面
- 左侧：设备列表和连接管理
- 右侧：日志查看和工具面板

### 设备管理
- 设备列表显示序列号、状态、品牌、型号
- 支持USB和WiFi连接类型
- 实时状态更新

### 日志查看
- 彩色日志显示
- 支持过滤和搜索
- 自动滚动和保存功能

## 常见问题

### Q: 程序无法检测到设备
**A:** 请检查：
- 设备是否已连接USB并开启USB调试
- ADB驱动是否正确安装
- 设备是否已授权调试

### Q: WiFi连接失败
**A:** 请确保：
- 设备和电脑在同一网络
- 设备已开启无线调试
- 防火墙没有阻止连接

### Q: Android Studio集成不工作
**A:** 请检查：
- Android Studio是否已正确安装
- 程序是否有权限启动外部程序
- Android Studio路径是否正确

## 开发说明

### 扩展功能
- 可以在`utils/adb_manager.py`中添加更多ADB命令
- 可以在`gui/main_window.py`中添加新的UI组件
- 支持添加新的设备管理功能

### 调试模式
- 在命令行运行程序可以看到详细的调试信息
- 日志会显示在"常规日志"标签页中

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件
- 提交Pull Request 