,
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e boolean eg5.c0.x(java.lang.String, java.lang.String, boolean) --\u003e [Regs] vreg0\u003d0x12DC0A00/java.lang.String \"Pipeline_SendMsgMgr_Main#yp4.h\" vreg1\u003d0x17505310/java.lang.String \"com.tencent.mm\" vreg2\u003d0x00000000
message": "[xiaojianbang InvokeWithArgArray] android.system.StructStat libcore.io.Linux.fstat(java.io.FileDescriptor) --\u003e void android.system.StructStat.\u003cinit\u003e(long, long, int, long, int, int, long, long, android.system.StructTimespec, android.system.StructTimespec, android.system.StructTimespec, long, long)
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e boolean eg5.c0.x(java.lang.String, java.lang.String, boolean) --\u003e [Regs] vreg0\u003d0x12DC0A00/java.lang.String \"Pipeline_SendMsgMgr_Main#yp4.h\" vreg1\u003d0x19EE4810/java.lang.String \"com.tencent\" vreg2\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D6310/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (l95.n) {c8467fd} m95.l@fe2efbd[Completed normally]\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void qk.t7.J1(java.lang.String) --\u003e [Regs] vreg0\u003d0x12D40B68/com.tencent.mm.storage.f8 vreg1\u003d0x1A5E65E0/java.lang.String \"wxid_uisi5rto449h22\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e void java.lang.StringBuilder.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x12E42B50/java.lang.StringBuilder
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D6370/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e vp4.z vp4.t.h() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x132C0A88/et0.o
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.Object vp4.z.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x12E40858/vp4.z vreg2\u003d0x1611E1B0/java.lang.String \"PPCKey_InitCreateTime\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] java.lang.Object vp4.z.d(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x1611E1B0/java.lang.String \"PPCKey_InitCreateTime\" vreg1\u003d0x703C7A88/java.lang.String \"key\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D6390/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.Object vp4.z.d(java.lang.String) --\u003e java.util.HashMap vp4.z.f() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x12E40858/vp4.z
message": "[xiaojianbang PerformCall] java.util.HashMap vp4.z.f() --\u003e java.lang.Object wc5.n.getValue() --\u003e [Regs] vreg0\u003d0x132407D8/wc5.n
message": "[xiaojianbang PerformCall] java.lang.Object vp4.z.d(java.lang.String) --\u003e java.lang.Object java.util.HashMap.get(java.lang.Object) --\u003e [Regs] vreg0\u003d0x132407F0/java.util.HashMap vreg1\u003d0x1611E1B0/java.lang.String \"PPCKey_InitCreateTime\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D6390/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e long java.lang.Long.longValue() --\u003e [Regs] vreg0\u003d0x13240880/java.lang.Long
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void qk.t7.e1(long) --\u003e [Regs] vreg0\u003d0x12D40B68/com.tencent.mm.storage.f8 vreg1\u003d0xE9AE3B11 vreg2\u003d0x00000197
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e java.nio.Buffer java.nio.CharBuffer.clear() --\u003e [Regs] vreg0\u003d0x13040828/java.nio.HeapCharBuffer
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void qk.t7.n1(int) --\u003e [Regs] vreg0\u003d0x12D40B68/com.tencent.mm.storage.f8 vreg1\u003d0x00000001
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.c(java.lang.String, long, long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A0BB5F8/ji.u vreg5\u003d0x131D6310/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (l95.n) {c8467fd} m95.l@fe2efbd[Completed normally]\" vreg6\u003d0xFB614E24 vreg7\u003d0x0000009D vreg8\u003d0xFB6A3C84 vreg9\u003d0x0000009D
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void qk.t7.d1(java.lang.String) --\u003e [Regs] vreg0\u003d0x12D40B68/com.tencent.mm.storage.f8 vreg1\u003d0x1B9F8D00/java.lang.String \"123456789\"
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void fi.e.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x131D5E00/fi.e
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void qk.t7.setType(int) --\u003e [Regs] vreg0\u003d0x12D40B68/com.tencent.mm.storage.f8 vreg1\u003d0x00000001
message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1000() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1002(fi.e) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void java.lang.StringBuilder.\u003cinit\u003e(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder vreg1\u003d0x130C4E08/java.lang.String \"msg content.size\u003d\"
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.String qk.t7.getContent() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x12D40B68/com.tencent.mm.storage.f8
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e int java.lang.String.length() --\u003e [Regs] vreg0\u003d0x1B9F8D00/java.lang.String \"123456789\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D6390/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(int) --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder vreg1\u003d0x00000009
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder vreg1\u003d0x130C4E80/java.lang.String \", createTime\u003d\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D69C0/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null: 2\"
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x12E42B50/java.lang.StringBuilder vreg1\u003d0x12DC0A00/java.lang.String \"Pipeline_SendMsgMgr_Main#yp4.h\"
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e long com.tencent.mm.storage.f8.getCreateTime() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x00000000 vreg6\u003d0x00000000 vreg7\u003d0x12D40B68/com.tencent.mm.storage.f8
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D69C0/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null: 2\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
message": "[xiaojianbang PerformCall] long com.tencent.mm.storage.f8.getCreateTime() --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C4EA0/com.tencent.mm.storage.d8
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D69C0/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null: 2\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D6A48/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] long com.tencent.mm.storage.f8.getCreateTime() --\u003e long qk.t7.getCreateTime() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x12D40B68/com.tencent.mm.storage.f8
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D6A68/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(long) --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder vreg1\u003d0xE9AE3B11 vreg2\u003d0x00000197
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D6A68/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(char) --\u003e [Regs] vreg0\u003d0x12E42B50/java.lang.StringBuilder vreg1\u003d0x00000040
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(int) --\u003e [Regs] vreg0\u003d0x12E42B50/java.lang.StringBuilder vreg1\u003d0x00BBF478
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1A0BB5F8/ji.u vreg4\u003d0x131D69C0/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null: 2\"
message": "[xiaojianbang PerformCall] java.lang.String rc3.a.c(java.lang.String, int) --\u003e java.lang.String java.lang.StringBuilder.toString() --\u003e [Regs] vreg0\u003d0x12E42B50/java.lang.StringBuilder
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e com.tencent.matrix.trace.core.AppMethodBeat com.tencent.matrix.trace.core.AppMethodBeat.getInstance() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x168AD4E0/com.tencent.matrix.trace.core.AppMethodBeat vreg6\u003d0x161A2338/java.lang.String \"AnrTracer#dispatchBegin\"
message": "[xiaojianbang PerformCall] void rc3.a.a(long) --\u003e int java.lang.StringBuffer.length() --\u003e [Regs] vreg0\u003d0x1A267018/java.lang.StringBuffer
message": "[xiaojianbang PerformCall] fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e void fi.e.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x131D6A88/fi.e vreg2\u003d0xFFFFFFFF
message": "[xiaojianbang PerformCall] void fi.e.\u003cinit\u003e(int) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x131D6A88/fi.e
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s vreg2\u003d0x00001388 vreg3\u003d0x00000000
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder vreg1\u003d0x130C5120/java.lang.String \" pipeline:\"
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t vreg2\u003d0x000007D0 vreg3\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D6A68/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e int java.lang.Object.hashCode() --\u003e [Regs] vreg0\u003d0x13240B30/vp4.r
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(int) --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder vreg1\u003d0x0BA46FAF
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.String java.lang.StringBuilder.toString() --\u003e [Regs] vreg0\u003d0x130C4DF8/java.lang.StringBuilder
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D70E8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null\"
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e void com.tencent.mm.sdk.platformtools.n2.j(java.lang.String, java.lang.String, java.lang.Object[]) --\u003e [Regs] vreg0\u003d0x130C5190/java.lang.String \"MicroMsg.SendMsgPPC.SendMsgFillCommonPPC\" vreg1\u003d0x130C5140/java.lang.String \"msg content.size\u003d9, createTime\u003d1751972199185 pipeline:195325871\" vreg2\u003d0x00000000
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D70E8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D70E8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7168/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7188/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D7188/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.c(java.lang.String, long, long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A0BB5F8/ji.u vreg5\u003d0x131D70E8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {e7ea348} null\" vreg6\u003d0xFB6F3994 vreg7\u003d0x0000009D vreg8\u003d0xFB7387AB vreg9\u003d0x0000009D
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void fi.e.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x131D6A88/fi.e
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e ur4.m ur4.n0.c(java.lang.Class) --\u003e [Regs] vreg0\u003d0x17BB2A50/java.lang.Class\u003cp23.m3\u003e
message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1000() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1002(fi.e) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s
message": "[xiaojianbang PerformCall] java.lang.Object et0.o.i(vp4.z, yp4.b, kotlin.coroutines.Continuation) --\u003e java.lang.String ct0.k1.db(com.tencent.mm.storage.f8) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x1812A608/ct0.k1 vreg6\u003d0x12D40B68/com.tencent.mm.storage.f8
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e boolean wg.p1.b() --\u003e [Regs] vreg0\u003d0x130405A8/wg.p1
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t
message": "[xiaojianbang PerformCall] java.lang.String ct0.k1.db(com.tencent.mm.storage.f8) --\u003e java.lang.Class java.lang.Object.getClass() --\u003e [Regs] vreg0\u003d0x1812A668/ng1.u0
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7188/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.String ct0.k1.db(com.tencent.mm.storage.f8) --\u003e ur4.m ur4.n0.c(java.lang.Class) --\u003e [Regs] vreg0\u003d0x1789D970/java.lang.Class\u003czs.m2\u003e
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D77B8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null: 2\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D77B8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null: 2\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D77B8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null: 2\" vreg1\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void zs.q0.\u003cclinit\u003e() --\u003e void zs.q0.\u003cinit\u003e(java.lang.String, int) --\u003e [Regs] vreg0\u003d0x130C5718/zs.q0 vreg1\u003d0x703B5EC0/java.lang.String \"INSTANCE\" vreg2\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void zs.q0.\u003cinit\u003e(java.lang.String, int) --\u003e void java.lang.Enum.\u003cinit\u003e(java.lang.String, int) --\u003e [Regs] vreg0\u003d0x130C5718/zs.q0 vreg1\u003d0x703B5EC0/java.lang.String \"INSTANCE\" vreg2\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7315,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e void wg.p1.c() --\u003e [Regs] vreg0\u003d0x130405A8/wg.p1
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7840/java.util.HashMap$KeyIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7860/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D7860/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1A0BB5F8/ji.u vreg4\u003d0x131D77B8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null: 2\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e com.tencent.matrix.trace.core.AppMethodBeat com.tencent.matrix.trace.core.AppMethodBeat.getInstance() --\u003e [Regs] vreg0\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] java.lang.Iterable zs.q0.all() --\u003e void zs.p0.\u003cinit\u003e(zs.n0) --\u003e [Regs] vreg0\u003d0x130C5A70/zs.p0 vreg1\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x168AD4E0/com.tencent.matrix.trace.core.AppMethodBeat vreg6\u003d0x161A2338/java.lang.String \"AnrTracer#dispatchBegin\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void zs.p0.\u003cinit\u003e(zs.n0) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C5A70/zs.p0
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e void fi.e.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x131D7880/fi.e vreg2\u003d0xFFFFFFFF
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.e.\u003cinit\u003e(int) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x131D7880/fi.e
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s vreg2\u003d0x00001388 vreg3\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t vreg2\u003d0x000007D0 vreg3\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7860/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] java.util.Iterator zs.p0.iterator() --\u003e void zs.o0.\u003cinit\u003e(zs.p0, zs.n0) --\u003e [Regs] vreg0\u003d0x130C5CF8/zs.o0 vreg1\u003d0x130C5A70/zs.p0 vreg2\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void zs.o0.\u003cinit\u003e(zs.p0, zs.n0) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C5CF8/zs.o0
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.view.VsyncWaiter$DisplayListener.onDisplayChanged(int) --\u003e android.view.Display android.hardware.display.DisplayManager.getDisplay(int) --\u003e [Regs] vreg0\u003d0x19F33298/android.hardware.display.DisplayManager vreg1\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.view.VsyncWaiter$DisplayListener.onDisplayChanged(int) --\u003e float android.view.Display.getRefreshRate() --\u003e [Regs] vreg0\u003d0x1A619388/android.view.Display
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.view.VsyncWaiter$DisplayListener.onDisplayChanged(int) --\u003e long io.flutter.view.VsyncWaiter.access$002(io.flutter.view.VsyncWaiter, long) --\u003e [Regs] vreg0\u003d0x168449C0/io.flutter.view.VsyncWaiter vreg1\u003d0x00FE5029 vreg2\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.view.VsyncWaiter$DisplayListener.onDisplayChanged(int) --\u003e io.flutter.embedding.engine.FlutterJNI io.flutter.view.VsyncWaiter.access$100(io.flutter.view.VsyncWaiter) --\u003e [Regs] vreg0\u003d0x168449C0/io.flutter.view.VsyncWaiter
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.view.VsyncWaiter$DisplayListener.onDisplayChanged(int) --\u003e void io.flutter.embedding.engine.FlutterJNI.setRefreshRateFPS(float) --\u003e [Regs] vreg0\u003d0x1A0BBFA0/io.flutter.embedding.engine.FlutterJNI vreg1\u003d0x42700001
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.embedding.engine.FlutterJNI.setRefreshRateFPS(float) --\u003e void io.flutter.embedding.engine.FlutterJNI.updateRefreshRate() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BBFA0/io.flutter.embedding.engine.FlutterJNI
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void io.flutter.embedding.engine.FlutterJNI.updateRefreshRate() --\u003e void io.flutter.embedding.engine.FlutterJNI.nativeUpdateRefreshRate(float) --\u003e [Regs] vreg0\u003d0x1A0BBFA0/io.flutter.embedding.engine.FlutterJNI vreg1\u003d0x42700001
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D7EE0/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D7EE0/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D7EE0/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null\" vreg1\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7F60/java.util.HashMap$KeyIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7F80/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D7F80/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.c(java.lang.String, long, long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A0BB5F8/ji.u vreg5\u003d0x131D7EE0/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.hardware.display.DisplayManagerGlobal$DisplayListenerDelegate) {8062408} null\" vreg6\u003d0xFB790724 vreg7\u003d0x0000009D vreg8\u003d0xFB7EC616 vreg9\u003d0x0000009D
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void fi.e.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x131D7880/fi.e
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1000() --\u003e [Regs] vreg0\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1002(fi.e) --\u003e [Regs] vreg0\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D7F80/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang InvokeWithArgArray] java.lang.Object java.lang.reflect.Constructor.newInstance0(java.lang.Object[]) --\u003e void java.lang.reflect.Proxy.\u003cinit\u003e(java.lang.reflect.InvocationHandler)
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D8440/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 30\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void java.lang.reflect.Proxy.\u003cinit\u003e(java.lang.reflect.InvocationHandler) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C7650/$Proxy2
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D8440/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 30\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void java.lang.reflect.Proxy.\u003cinit\u003e(java.lang.reflect.InvocationHandler) --\u003e java.lang.Object java.util.Objects.requireNonNull(java.lang.Object) --\u003e [Regs] vreg0\u003d0x130C75A0/libcore.reflect.AnnotationFactory
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D8440/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 30\" vreg1\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang InvokeWithArgArray] java.lang.Class[] vr4.b.dependencies() --\u003e java.lang.Object java.lang.reflect.Proxy.invoke(java.lang.reflect.Proxy, java.lang.reflect.Method, java.lang.Object[])
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D84B0/java.util.HashMap$KeyIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D84D0/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D84D0/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1A0BB5F8/ji.u vreg4\u003d0x131D8440/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 30\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e com.tencent.matrix.trace.core.AppMethodBeat com.tencent.matrix.trace.core.AppMethodBeat.getInstance() --\u003e [Regs] vreg0\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x168AD4E0/com.tencent.matrix.trace.core.AppMethodBeat vreg6\u003d0x161A2338/java.lang.String \"AnrTracer#dispatchBegin\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e void fi.e.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x131D84F0/fi.e vreg2\u003d0xFFFFFFFF
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.e.\u003cinit\u003e(int) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x131D84F0/fi.e
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s vreg2\u003d0x00001388 vreg3\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t vreg2\u003d0x000007D0 vreg3\u003d0x00000000
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D84D0/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D8A10/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D8A10/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7370,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void java.util.concurrent.ForkJoinTask$RunnableExecuteAction.\u003cinit\u003e(java.lang.Runnable) --\u003e void java.util.concurrent.ForkJoinTask.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C9390/java.util.concurrent.ForkJoinTask$RunnableExecuteAction
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7289,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 245000000
        }
      },
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D8A10/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\" vreg1\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D8A78/java.util.HashMap$KeyIterator
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D8A98/java.util.HashMap$ValueIterator
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D8A98/java.util.HashMap$ValueIterator
    {
      "header": {
        "logLevel": "ERROR",
        "pid": 7289,
        "tid": 7442,
        "applicationId": "com.tencent.mm",
        "processName": "com.tencent.mm",
        "tag": "com.tencent.mm",
        "timestamp": {
          "seconds": 1751972200,
          "nanos": 246000000
        }
      },
      "message": "[xiaojianbang PerformCall] boolean java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec() --\u003e void ur4.t.run() --\u003e [Regs] vreg0\u003d0x130C87C8/ur4.t
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e boolean java.lang.Character.isWhitespace(char) --\u003e [Regs] vreg0\u003d0x00000052
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.c(java.lang.String, long, long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A0BB5F8/ji.u vreg5\u003d0x131D8A10/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\" vreg6\u003d0xFB82618C vreg7\u003d0x0000009D vreg8\u003d0xFB8756AC vreg9\u003d0x0000009D
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void fi.e.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x131D84F0/fi.e
    {
    
      "message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1000() --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1002(fi.e) --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D8A98/java.util.HashMap$ValueIterator
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D8F58/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 35\"
    {
    
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D8F58/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 35\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
    {
    
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D8F58/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 35\" vreg1\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D8FC8/java.util.HashMap$KeyIterator
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] java.lang.String ct0.k1.db(com.tencent.mm.storage.f8) --\u003e java.lang.String qk.t7.P0() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x12D40B68/com.tencent.mm.storage.f8
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D8FE8/java.util.HashMap$ValueIterator
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D8FE8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.String ct0.k1.db(com.tencent.mm.storage.f8) --\u003e java.lang.Class java.lang.Object.getClass() --\u003e [Regs] vreg0\u003d0x130C5D40/ys.o
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] java.lang.String ct0.k1.db(com.tencent.mm.storage.f8) --\u003e boolean ds0.z.h(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A5E65E0/java.lang.String \"wxid_uisi5rto449h22\"
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] boolean ds0.z.h(java.lang.String) --\u003e hj.o ds0.z.b(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A5E65E0/java.lang.String \"wxid_uisi5rto449h22\"
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1A0BB5F8/ji.u vreg4\u003d0x131D8F58/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null: 35\"
message": "[xiaojianbang PerformCall] hj.o ds0.z.b(java.lang.String) --\u003e ds0.x ds0.u2.ec() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e com.tencent.matrix.trace.core.AppMethodBeat com.tencent.matrix.trace.core.AppMethodBeat.getInstance() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] ds0.x ds0.u2.ec() --\u003e xe0.m xe0.j1.b() --\u003e [Regs]
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x168AD4E0/com.tencent.matrix.trace.core.AppMethodBeat vreg6\u003d0x161A2338/java.lang.String \"AnrTracer#dispatchBegin\"
message": "[xiaojianbang PerformCall] ds0.x ds0.u2.ec() --\u003e void xe0.m.c() --\u003e [Regs] vreg0\u003d0x17446AB8/xe0.m
    {
    
      "message": "[xiaojianbang PerformCall] fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e void fi.e.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x131D9008/fi.e vreg2\u003d0xFFFFFFFF
message": "[xiaojianbang PerformCall] ds0.x ds0.u2.ec() --\u003e ds0.u2 ds0.u2.Bc() --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.e.\u003cinit\u003e(int) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x131D9008/fi.e
message": "[xiaojianbang PerformCall] ds0.u2 ds0.u2.Bc() --\u003e ur4.m ur4.n0.c(java.lang.Class) --\u003e [Regs] vreg0\u003d0x17993610/java.lang.Class\u003cds0.u2\u003e
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s vreg2\u003d0x00001388 vreg3\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t vreg2\u003d0x000007D0 vreg3\u003d0x00000000
message": "[xiaojianbang PerformCall] ds0.x ds0.u2.ec() --\u003e ds0.u2 ds0.u2.Bc() --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D8FE8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] ds0.u2 ds0.u2.Bc() --\u003e ur4.m ur4.n0.c(java.lang.Class) --\u003e [Regs] vreg0\u003d0x17993610/java.lang.Class\u003cds0.u2\u003e
message": "[xiaojianbang PerformCall] hj.o ds0.z.b(java.lang.String) --\u003e hj.o ds0.x.j1(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1616CFF0/ds0.x vreg4\u003d0x1A5E65E0/java.lang.String \"wxid_uisi5rto449h22\"
message": "[xiaojianbang PerformCall] hj.o ds0.x.j1(java.lang.String) --\u003e void hj.o.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C93F0/hj.o
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D95D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\"
message": "[xiaojianbang PerformCall] void hj.o.\u003cinit\u003e() --\u003e void qk.e1.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x130C93F0/hj.o
    {
    
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D95D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
message": "[xiaojianbang PerformCall] void qk.e1.\u003cinit\u003e() --\u003e void aq4.f0.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C93F0/hj.o
    {
    
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D95D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] hj.o ds0.x.j1(java.lang.String) --\u003e boolean aq4.l0.get(aq4.f0, java.lang.String[]) --\u003e [Regs] vreg0\u003d0x1616CFF0/ds0.x vreg1\u003d0x130C93F0/hj.o vreg2\u003d0x130C9478/java.lang.String[]
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e boolean java.nio.Buffer.hasRemaining() --\u003e [Regs] vreg0\u003d0x13040828/java.nio.HeapCharBuffer
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] android.content.ContentValues hj.o.convertTo() --\u003e android.content.ContentValues qk.e1.convertTo() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x130C93F0/hj.o
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D9640/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x70E812B8/java.lang.String \"username\" vreg2\u003d0x1A5E65E0/java.lang.String \"wxid_uisi5rto449h22\"
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D9660/java.util.HashMap$ValueIterator
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D9660/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x17552278/java.lang.String \"appId\" vreg2\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC680/java.lang.String \"brandList\" vreg2\u003d0x703AB588/java.lang.String \"\"
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC6C0/java.lang.String \"brandListVersion\" vreg2\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.c(java.lang.String, long, long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A0BB5F8/ji.u vreg5\u003d0x131D95D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} null\" vreg6\u003d0xFB8BDDBB vreg7\u003d0x0000009D vreg8\u003d0xFB91CE1E vreg9\u003d0x0000009D
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC6A0/java.lang.String \"brandListContent\" vreg2\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void fi.e.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x131D9008/fi.e
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1000() --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1002(fi.e) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC640/java.lang.String \"brandFlag\" vreg2\u003d0x702E6070/java.lang.Integer
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x175EC530/java.lang.String \"extInfo\" vreg2\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC660/java.lang.String \"brandInfo\" vreg2\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D9660/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x19FD4CD0/java.lang.String \"brandIconURL\" vreg2\u003d0x00000000
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Long java.lang.Long.valueOf(long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Long) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x175310E8/java.lang.String \"updateTime\" vreg2\u003d0x702E7480/java.lang.Long
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131D9DA8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f: 0\"
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131D9DA8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f: 0\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EECE30/java.lang.String \"hadAlert\" vreg2\u003d0x702E6070/java.lang.Integer
    {
    
      "message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
    {
    
      "message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131D9DA8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f: 0\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EECB18/java.lang.String \"acceptType\" vreg2\u003d0x702E6070/java.lang.Integer
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e java.nio.CharBuffer java.nio.HeapCharBuffer.put(char) --\u003e [Regs] vreg0\u003d0x13040828/java.nio.HeapCharBuffer vreg1\u003d0x00000052
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x70E5F3E0/java.lang.String \"type\" vreg2\u003d0x702E6070/java.lang.Integer
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D9E60/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e boolean wg.p1.b() --\u003e [Regs] vreg0\u003d0x130405A8/wg.p1
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x703CB1E0/java.lang.String \"status\" vreg2\u003d0x702E6070/java.lang.Integer
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EED6C0/java.lang.String \"enterpriseFather\" vreg2\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC760/java.lang.String \"kfWorkerId\" vreg2\u003d0x00000000
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e void wg.p1.c() --\u003e [Regs] vreg0\u003d0x130405A8/wg.p1
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D9E80/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131D9E80/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEBC40/java.lang.String \"specialType\" vreg2\u003d0x702E6070/java.lang.Integer
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEB200/java.lang.String \"attrSyncVersion\" vreg2\u003d0x00000000
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e boolean java.lang.Character.isWhitespace(char) --\u003e [Regs] vreg0\u003d0x00000020
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Long java.lang.Long.valueOf(long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Long) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x15EEC870/java.lang.String \"incrementUpdateTime\" vreg2\u003d0x702E7480/java.lang.Long
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1A0BB5F8/ji.u vreg4\u003d0x131D9DA8/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f: 0\"
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e void wg.p1.g() --\u003e [Regs] vreg0\u003d0x130405A8/wg.p1
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e com.tencent.matrix.trace.core.AppMethodBeat com.tencent.matrix.trace.core.AppMethodBeat.getInstance() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] android.content.ContentValues qk.e1.convertTo() --\u003e void android.content.ContentValues.put(java.lang.String, java.lang.Integer) --\u003e [Regs] vreg0\u003d0x130C9488/android.content.ContentValues vreg1\u003d0x19FD29F0/java.lang.String \"bitFlag\" vreg2\u003d0x702E6070/java.lang.Integer
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x168AD4E0/com.tencent.matrix.trace.core.AppMethodBeat vreg6\u003d0x161A2338/java.lang.String \"AnrTracer#dispatchBegin\"
message": "[xiaojianbang PerformCall] fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e void fi.e.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x131D9EA0/fi.e vreg2\u003d0xFFFFFFFF
message": "[xiaojianbang PerformCall] void fi.e.\u003cinit\u003e(int) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x131D9EA0/fi.e
message": "[xiaojianbang PerformCall] android.database.Cursor qr4.a0.l(java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int) --\u003e android.database.Cursor qr4.a0.b(java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int, java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x00000000 vreg6\u003d0x00000000 vreg7\u003d0x00000000 vreg8\u003d0x00000000 vreg9\u003d0x00000000 vreg10\u003d0x00000000 vreg11\u003d0x00000000 vreg12\u003d0x00000000 vreg13\u003d0x17341EC0/qr4.a0 vreg14\u003d0x15EEC400/java.lang.String \"bizinfo\" vreg15\u003d0x1638CB18/java.lang.String[] vreg16\u003d0x130C9800/java.lang.String \"username \u003d ?\" vreg17\u003d0x130C9820/java.lang.String[] vreg18\u003d0x00000000 vreg19\u003d0x00000000 vreg20\u003d0x00000000 vreg21\u003d0x00000002 vreg22\u003d0x00000000
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s vreg2\u003d0x00001388 vreg3\u003d0x00000000
message": "[xiaojianbang PerformCall] android.database.Cursor qr4.a0.b(java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int, java.lang.String) --\u003e boolean qr4.a0.A() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x17341EC0/qr4.a0
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e boolean android.os.Handler.postDelayed(java.lang.Runnable, long) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t vreg2\u003d0x000007D0 vreg3\u003d0x00000000
message": "[xiaojianbang PerformCall] java.nio.CharBuffer wg.p1.e(java.nio.CharBuffer) --\u003e java.nio.Buffer java.nio.CharBuffer.flip() --\u003e [Regs] vreg0\u003d0x13040828/java.nio.HeapCharBuffer
message": "[xiaojianbang PerformCall] boolean qr4.a0.A() --\u003e boolean qr4.f.n() --\u003e [Regs] vreg0\u003d0x17341BA0/qr4.f
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131D9E80/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void wg.p1.a() --\u003e void java.io.RandomAccessFile.close() --\u003e [Regs] vreg0\u003d0x130405F8/java.io.RandomAccessFile
message": "[xiaojianbang PerformCall] android.database.Cursor qr4.a0.b(java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int, java.lang.String) --\u003e void qr4.a0$$a.\u003cinit\u003e(qr4.a0, java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int, java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9830/qr4.a0$$a vreg1\u003d0x17341EC0/qr4.a0 vreg2\u003d0x15EEC400/java.lang.String \"bizinfo\" vreg3\u003d0x1638CB18/java.lang.String[] vreg4\u003d0x130C9800/java.lang.String \"username \u003d ?\" vreg5\u003d0x130C9820/java.lang.String[] vreg6\u003d0x00000000 vreg7\u003d0x00000000 vreg8\u003d0x00000000 vreg9\u003d0x00000002 vreg10\u003d0x00000000
message": "[xiaojianbang PerformCall] void qr4.a0$$a.\u003cinit\u003e(qr4.a0, java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int, java.lang.String) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x130C9830/qr4.a0$$a
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131DA5D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f\"
message": "[xiaojianbang PerformCall] android.database.Cursor qr4.a0.b(java.lang.String, java.lang.String[], java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, int, java.lang.String) --\u003e java.lang.Object qr4.a0$$a.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x00000000 vreg6\u003d0x00000000 vreg7\u003d0x00000000 vreg8\u003d0x00000000 vreg9\u003d0x00000000 vreg10\u003d0x00000000 vreg11\u003d0x00000000 vreg12\u003d0x00000000 vreg13\u003d0x00000000 vreg14\u003d0x00000000 vreg15\u003d0x00000000 vreg16\u003d0x130C9830/qr4.a0$$a
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131DA5D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
message": "[xiaojianbang PerformCall] java.lang.Object qr4.a0$$a.a() --\u003e java.lang.Class java.lang.Object.getClass() --\u003e [Regs] vreg0\u003d0x17341EC0/qr4.a0
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] java.lang.Object qr4.a0$$a.a() --\u003e boolean op4.c.a() --\u003e [Regs]
message": "[xiaojianbang InvokeWithArgArray] void libcore.io.Linux.close(java.io.FileDescriptor) --\u003e long java.io.FileDescriptor.getOwnerId$()
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131DA5D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] java.lang.Object qr4.a0$$a.a() --\u003e boolean qr4.f.m() --\u003e [Regs]
message": "[xiaojianbang PerformCall] rg.f rg.g.m(java.lang.String, int) --\u003e long wg.t1.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x13040868/wg.t1
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] java.lang.Object qr4.a0$$a.a() --\u003e com.tencent.wcdb.Cursor com.tencent.wcdb.database.SQLiteDatabase.queryWithFactory(com.tencent.wcdb.database.SQLiteDatabase$CursorFactory, boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.Object[], java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x00000000 vreg6\u003d0x00000000 vreg7\u003d0x00000000 vreg8\u003d0x00000000 vreg9\u003d0x00000000 vreg10\u003d0x00000000 vreg11\u003d0x00000000 vreg12\u003d0x17341B30/com.tencent.wcdb.database.SQLiteDatabase vreg13\u003d0x00000000 vreg14\u003d0x00000000 vreg15\u003d0x15EEC400/java.lang.String \"bizinfo\" vreg16\u003d0x1638CB18/java.lang.String[] vreg17\u003d0x130C9800/java.lang.String \"username \u003d ?\" vreg18\u003d0x130C9820/java.lang.String[] vreg19\u003d0x00000000 vreg20\u003d0x00000000 vreg21\u003d0x00000000 vreg22\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131DA688/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] rg.f rg.g.m(java.lang.String, int) --\u003e java.lang.Long java.lang.Long.valueOf(long) --\u003e [Regs] vreg0\u003d0x0000030A vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] com.tencent.wcdb.Cursor com.tencent.wcdb.database.SQLiteDatabase.queryWithFactory(com.tencent.wcdb.database.SQLiteDatabase$CursorFactory, boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.Object[], java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e com.tencent.wcdb.Cursor com.tencent.wcdb.database.SQLiteDatabase.queryWithFactory(com.tencent.wcdb.database.SQLiteDatabase$CursorFactory, boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.Object[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.tencent.wcdb.support.CancellationSignal) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x00000000 vreg6\u003d0x00000000 vreg7\u003d0x00000000 vreg8\u003d0x00000000 vreg9\u003d0x17341B30/com.tencent.wcdb.database.SQLiteDatabase vreg10\u003d0x00000000 vreg11\u003d0x00000000 vreg12\u003d0x15EEC400/java.lang.String \"bizinfo\" vreg13\u003d0x1638CB18/java.lang.String[] vreg14\u003d0x130C9800/java.lang.String \"username \u003d ?\" vreg15\u003d0x130C9820/java.lang.String[] vreg16\u003d0x00000000 vreg17\u003d0x00000000 vreg18\u003d0x00000000 vreg19\u003d0x00000000 vreg20\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
message": "[xiaojianbang PerformCall] rg.f rg.g.m(java.lang.String, int) --\u003e rg.x2 rg.x2.b(java.lang.Number) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x130408F0/java.lang.Long
message": "[xiaojianbang PerformCall] com.tencent.wcdb.Cursor com.tencent.wcdb.database.SQLiteDatabase.queryWithFactory(com.tencent.wcdb.database.SQLiteDatabase$CursorFactory, boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.Object[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.tencent.wcdb.support.CancellationSignal) --\u003e void com.tencent.wcdb.database.SQLiteClosable.acquireReference() --\u003e [Regs] vreg0\u003d0x17341B30/com.tencent.wcdb.database.SQLiteDatabase
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] rg.x2 rg.x2.b(java.lang.Number) --\u003e void rg.w2.\u003cinit\u003e(java.lang.Long) --\u003e [Regs] vreg0\u003d0x13040900/rg.w2 vreg1\u003d0x130408F0/java.lang.Long
message": "[xiaojianbang PerformCall] com.tencent.wcdb.Cursor com.tencent.wcdb.database.SQLiteDatabase.queryWithFactory(com.tencent.wcdb.database.SQLiteDatabase$CursorFactory, boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.Object[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.tencent.wcdb.support.CancellationSignal) --\u003e java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x15EEC400/java.lang.String \"bizinfo\" vreg4\u003d0x1638CB18/java.lang.String[] vreg5\u003d0x130C9800/java.lang.String \"username \u003d ?\" vreg6\u003d0x00000000 vreg7\u003d0x00000000 vreg8\u003d0x00000000 vreg9\u003d0x00000000
message": "[xiaojianbang PerformCall] void rg.w2.\u003cinit\u003e(java.lang.Long) --\u003e void rg.x2.\u003cinit\u003e(java.lang.Number) --\u003e [Regs] vreg0\u003d0x13040900/rg.w2 vreg1\u003d0x130408F0/java.lang.Long
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131DA6A8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e boolean android.text.TextUtils.isEmpty(java.lang.CharSequence) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void rg.x2.\u003cinit\u003e(java.lang.Number) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x13040900/rg.w2
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131DA6A8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void kg.d.x(java.lang.String, int) --\u003e void kotlin.jvm.internal.o.f(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x1981F248/java.util.concurrent.ConcurrentHashMap vreg1\u003d0x1981F288/java.lang.String \"mTaskJiffiesTrace\"
message": "[xiaojianbang PerformCall] java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e boolean android.text.TextUtils.isEmpty(java.lang.CharSequence) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e boolean android.text.TextUtils.isEmpty(java.lang.CharSequence) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void kg.d.x(java.lang.String, int) --\u003e java.lang.Integer java.lang.Integer.valueOf(int) --\u003e [Regs] vreg0\u003d0x02A5795A
message": "[xiaojianbang PerformCall] java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e void java.lang.StringBuilder.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x00000078
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x70E379B8/java.lang.String \"SELECT \"
message": "[xiaojianbang PerformCall] void kg.d.x(java.lang.String, int) --\u003e java.lang.Object java.util.concurrent.ConcurrentHashMap.put(java.lang.Object, java.lang.Object) --\u003e [Regs] vreg0\u003d0x1981F248/java.util.concurrent.ConcurrentHashMap vreg1\u003d0x13040910/java.lang.Integer vreg2\u003d0x13040288/rg.f
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.c(java.lang.String, long, long) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1A0BB5F8/ji.u vreg5\u003d0x131DA5D8/java.lang.String \"\u003c\u003c\u003c\u003c\u003c Finished to Handler (android.view.ViewRootImpl$ViewRootHandler) {e3cf1e7} android.graphics.HardwareRendererObserver$$ExternalSyntheticLambda0@2bf865f\" vreg6\u003d0xFB993305 vreg7\u003d0x0000009D vreg8\u003d0xFBA55A98 vreg9\u003d0x0000009D
message": "[xiaojianbang PerformCall] java.lang.String com.tencent.wcdb.database.SQLiteQueryBuilder.buildQueryString(boolean, java.lang.String, java.lang.String[], java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String) --\u003e void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x130C9860/java.lang.StringBuilder vreg5\u003d0x1638CB18/java.lang.String[]
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void fi.e.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x131D9EA0/fi.e
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x70E812B8/java.lang.String \"username\"
message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1000() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.e.a() --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.access$1002(fi.e) --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] java.lang.Throwable kotlinx.coroutines.r.getExceptionalResult$kotlinx_coroutines_core(java.lang.Object) --\u003e java.lang.Throwable kotlinx.coroutines.m1.getExceptionalResult$kotlinx_coroutines_core(java.lang.Object) --\u003e [Regs] vreg0\u003d0x12DC08F0/kotlinx.coroutines.r vreg1\u003d0x12E418C0/kotlinx.coroutines.c0
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D328/android.os.Handler vreg1\u003d0x1A0BB618/ji.s
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x17552278/java.lang.String \"appId\"
message": "[xiaojianbang PerformCall] void ji.u.c(java.lang.String, long, long) --\u003e void android.os.Handler.removeCallbacks(java.lang.Runnable) --\u003e [Regs] vreg0\u003d0x1618D348/android.os.Handler vreg1\u003d0x1A0BB628/ji.t
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x15EEC680/java.lang.String \"brandList\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131DA6A8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x15EEC6C0/java.lang.String \"brandListVersion\"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x15EEC6A0/java.lang.String \"brandListContent\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void r25.l0.println(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x1737E018/r25.l0 vreg5\u003d0x131DB028/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.os.Handler) {a022d1d} com.android.internal.inputmethod.RemoteInputConnectionImpl$$ExternalSyntheticLambda2@2f758ac: 0\"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e void kotlin.jvm.internal.o.g(java.lang.Object, java.lang.String) --\u003e [Regs] vreg0\u003d0x131DB028/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.os.Handler) {a022d1d} com.android.internal.inputmethod.RemoteInputConnectionImpl$$ExternalSyntheticLambda2@2f758ac: 0\" vreg1\u003d0x703CCE40/java.lang.String \"x\"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x15EEC640/java.lang.String \"brandFlag\"
message": "[xiaojianbang PerformCall] void r25.l0.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e char java.lang.String.charAt(int) --\u003e [Regs] vreg0\u003d0x131DB028/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.os.Handler) {a022d1d} com.android.internal.inputmethod.RemoteInputConnectionImpl$$ExternalSyntheticLambda2@2f758ac: 0\" vreg1\u003d0x00000000
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashSet.iterator() --\u003e [Regs] vreg0\u003d0x167F1070/java.util.HashSet
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x175EC530/java.lang.String \"extInfo\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131DB0D8/java.util.HashMap$KeyIterator
message": "[xiaojianbang PerformCall] java.lang.Object np4.q.invokeSuspend(java.lang.Object) --\u003e void kotlin.ResultKt.throwOnFailure(java.lang.Object) --\u003e [Regs] vreg0\u003d0x174A32A8/wc5.f0
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Collection java.util.HashMap.values() --\u003e [Regs] vreg0\u003d0x167F1080/java.util.HashMap
message": "[xiaojianbang PerformCall] java.lang.Object np4.q.invokeSuspend(java.lang.Object) --\u003e java.lang.Object hg5.a.a(kotlin.coroutines.Continuation) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x13280838/hg5.a vreg6\u003d0x132413B0/np4.q
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.util.Iterator java.util.HashMap$Values.iterator() --\u003e [Regs] vreg0\u003d0x167F10A8/java.util.HashMap$Values
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x15EEC660/java.lang.String \"brandInfo\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean java.util.HashMap$HashIterator.hasNext() --\u003e [Regs] vreg0\u003d0x131DB0F8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] java.lang.Object hg5.a.a(kotlin.coroutines.Continuation) --\u003e java.lang.Object hg5.k.D() --\u003e [Regs] vreg0\u003d0x132410C0/hg5.m0
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x19FD4CD0/java.lang.String \"brandIconURL\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e java.lang.Object java.util.HashMap$ValueIterator.next() --\u003e [Regs] vreg0\u003d0x131DB0F8/java.util.HashMap$ValueIterator
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] java.lang.Object hg5.a.a(kotlin.coroutines.Continuation) --\u003e kotlin.coroutines.Continuation cd5.f.b(kotlin.coroutines.Continuation) --\u003e [Regs] vreg0\u003d0x132413B0/np4.q
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e boolean ji.u.a() --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x1A0BB5F8/ji.u
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x175310E8/java.lang.String \"updateTime\"
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e long java.lang.System.nanoTime() --\u003e [Regs]
message": "[xiaojianbang PerformCall] java.lang.Object hg5.a.a(kotlin.coroutines.Continuation) --\u003e kotlinx.coroutines.r kotlinx.coroutines.t.a(kotlin.coroutines.Continuation) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x132413D8/kotlinx.coroutines.internal.DispatchedContinuation
message": "[xiaojianbang PerformCall] void fi.i.println(java.lang.String) --\u003e void ji.u.d(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x1A0BB5F8/ji.u vreg4\u003d0x131DB028/java.lang.String \"\u003e\u003e\u003e\u003e\u003e Dispatching to Handler (android.os.Handler) {a022d1d} com.android.internal.inputmethod.RemoteInputConnectionImpl$$ExternalSyntheticLambda2@2f758ac: 0\"
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x703AD258/java.lang.String \", \"
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e com.tencent.matrix.trace.core.AppMethodBeat com.tencent.matrix.trace.core.AppMethodBeat.getInstance() --\u003e [Regs] vreg0\u003d0x00000000
message": "[xiaojianbang PerformCall] kotlinx.coroutines.r kotlinx.coroutines.t.a(kotlin.coroutines.Continuation) --\u003e kotlinx.coroutines.r kotlinx.coroutines.internal.DispatchedContinuation.claimReusableCancellableContinuation() --\u003e [Regs] vreg0\u003d0x132413D8/kotlinx.coroutines.internal.DispatchedContinuation
message": "[xiaojianbang PerformCall] void com.tencent.wcdb.database.SQLiteQueryBuilder.appendColumns(java.lang.StringBuilder, java.lang.String[]) --\u003e java.lang.StringBuilder java.lang.StringBuilder.append(java.lang.String) --\u003e [Regs] vreg0\u003d0x130C9860/java.lang.StringBuilder vreg1\u003d0x15EECE30/java.lang.String \"hadAlert\"
message": "[xiaojianbang PerformCall] void ji.u.d(java.lang.String) --\u003e fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x00000000 vreg2\u003d0x00000000 vreg3\u003d0x00000000 vreg4\u003d0x00000000 vreg5\u003d0x168AD4E0/com.tencent.matrix.trace.core.AppMethodBeat vreg6\u003d0x161A2338/java.lang.String \"AnrTracer#dispatchBegin\"
message": "[xiaojianbang PerformCall] kotlinx.coroutines.r kotlinx.coroutines.t.a(kotlin.coroutines.Continuation) --\u003e void kotlinx.coroutines.r.\u003cinit\u003e(kotlin.coroutines.Continuation, int) --\u003e [Regs] vreg0\u003d0x13040938/kotlinx.coroutines.r vreg1\u003d0x132413D8/kotlinx.coroutines.internal.DispatchedContinuation vreg2\u003d0x00000002
message": "[xiaojianbang PerformCall] fi.e com.tencent.matrix.trace.core.AppMethodBeat.maskIndex(java.lang.String) --\u003e void fi.e.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x00000000 vreg1\u003d0x131DB118/fi.e vreg2\u003d0xFFFFFFFF
message": "[xiaojianbang PerformCall] void kotlinx.coroutines.r.\u003cinit\u003e(kotlin.coroutines.Continuation, int) --\u003e void kotlinx.coroutines.m1.\u003cinit\u003e(int) --\u003e [Regs] vreg0\u003d0x13040938/kotlinx.coroutines.r vreg1\u003d0x00000002
message": "[xiaojianbang PerformCall] void fi.e.\u003cinit\u003e(int) --\u003e void java.lang.Object.\u003cinit\u003e() --\u003e [Regs] vreg0\u003d0x131DB118/fi.e
