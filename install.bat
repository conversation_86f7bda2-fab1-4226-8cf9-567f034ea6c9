@echo off
echo ========================================
echo ADB工具安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 正在检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到pip，请检查Python安装
    pause
    exit /b 1
)

echo 正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法:
echo   双击 run.bat 启动程序
echo   或者运行: python main.py
echo.
pause 