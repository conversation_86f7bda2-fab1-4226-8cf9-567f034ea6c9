# JEDX MCP Server 分析文档

## 概述
这是一个基于FastMCP的JEDX（JADX AI）MCP服务器实现，用于与JADX反编译器插件进行交互。该服务器提供了完整的Android应用程序逆向工程API接口。

## 技术架构

### 核心依赖
- **fastmcp**: MCP协议实现框架
- **httpx**: 异步HTTP客户端
- **logging**: 日志记录

### 服务端配置
- **基础URL**: `http://127.0.0.1:8650`
- **传输方式**: stdio
- **日志级别**: ERROR

## 功能模块分析

### 1. 核心通信模块
```python
async def get_from_jadx(endpoint: str, params: dict = {}) -> Union[str, dict]
```
- 统一的JADX插件HTTP请求处理器
- 完整的异常处理和错误日志记录
- 60秒超时设置

### 2. 类级别操作工具

#### 2.1 获取当前类
- **工具**: `fetch_current_class`
- **功能**: 获取JADX-GUI中当前选中的类及其代码
- **端点**: `/current-class`

#### 2.2 获取类源码
- **工具**: `get_class_source`
- **功能**: 获取指定类的Java源代码
- **端点**: `/class-source`
- **参数**: `class_name`

#### 2.3 获取类的Smali代码
- **工具**: `get_smali_of_class`
- **功能**: 获取指定类的Smali汇编代码
- **端点**: `/smali-of-class`
- **参数**: `class_name`

#### 2.4 获取所有类列表
- **工具**: `get_all_classes`
- **功能**: 获取项目中所有类的列表
- **端点**: `/all-classes`
- **分页支持**: offset, count参数

### 3. 方法级别操作工具

#### 3.1 根据名称获取方法
- **工具**: `get_method_by_name`
- **功能**: 从特定类中获取方法源码
- **端点**: `/method-by-name`
- **参数**: `class_name`, `method_name`

#### 3.2 搜索方法
- **工具**: `search_method_by_name`
- **功能**: 跨所有类搜索方法名
- **端点**: `/search-method`
- **参数**: `method_name`
- **分页支持**: offset, count参数

#### 3.3 获取类的所有方法
- **工具**: `get_methods_of_class`
- **功能**: 列出指定类的所有方法名
- **端点**: `/methods-of-class`
- **参数**: `class_name`
- **分页支持**: offset, count参数

### 4. 字段级别操作工具

#### 4.1 获取类的所有字段
- **工具**: `get_fields_of_class`
- **功能**: 列出指定类的所有字段名
- **端点**: `/fields-of-class`
- **参数**: `class_name`
- **分页支持**: offset, count参数

### 5. 文本选择工具

#### 5.1 获取选中文本
- **工具**: `get_selected_text`
- **功能**: 获取当前在反编译代码视图中选中的文本
- **端点**: `/selected-text`

### 6. Android资源操作工具

#### 6.1 获取AndroidManifest.xml
- **工具**: `get_android_manifest`
- **功能**: 获取AndroidManifest.xml内容
- **端点**: `/manifest`

#### 6.2 获取字符串资源
- **工具**: `get_strings`
- **功能**: 获取strings.xml文件内容
- **端点**: `/strings`

#### 6.3 获取所有资源文件名
- **工具**: `get_all_resource_file_names`
- **功能**: 获取应用中所有资源文件的名称
- **端点**: `/list-all-resource-files-names`

#### 6.4 获取资源文件内容
- **工具**: `get_resource_file`
- **功能**: 获取指定资源文件的内容
- **端点**: `/get-resource-file`
- **参数**: `resource_name`

### 7. 主应用程序分析工具

#### 7.1 获取主应用程序类名
- **工具**: `get_main_application_classes_names`
- **功能**: 基于AndroidManifest.xml中的包名获取主应用程序类名
- **端点**: `/main-application-classes-names`
- **分页支持**: offset, count参数

#### 7.2 获取主应用程序类代码
- **工具**: `get_main_application_classes_code`
- **功能**: 获取主应用程序包下所有类的源代码
- **端点**: `/main-application-classes-code`
- **分页支持**: offset, count参数

#### 7.3 获取主Activity类
- **工具**: `get_main_activity_class`
- **功能**: 获取AndroidManifest.xml中定义的主Activity类
- **端点**: `/main-activity`

## 安全性评估

### 防御性设计特点
1. **完整的异常处理**: 所有HTTP请求都有完整的异常捕获和错误报告
2. **参数验证**: offset和count参数有边界检查
3. **超时控制**: 60秒HTTP请求超时防止长时间挂起
4. **日志记录**: 错误级别日志记录所有异常情况

### 潜在安全考虑
- 服务器固定监听本地端口8650，需要确保该端口访问控制
- 所有工具都是查询性质，不包含修改操作，降低了安全风险
- 适合白帽安全研究和恶意软件分析场景

## 使用场景

### 1. 恶意软件分析
- 快速提取Android应用的核心组件
- 分析应用的入口点和关键功能
- 获取资源文件进行深度分析

### 2. 安全审计
- 检查应用的权限和配置
- 分析潜在的安全漏洞
- 代码模式识别

### 3. 逆向工程研究
- 理解应用架构和设计模式
- 提取算法和业务逻辑
- 进行比较分析

## 部署和配置

### 启动方式
```bash
python jadx_mcp_server.py
```

### 环境要求
- Python >= 3.10
- 运行中的JADX-AI-MCP插件（端口8650）

### 日志配置
- 默认ERROR级别日志
- 控制台输出格式化日志
- 时间戳和级别信息

## 技术特点

### 1. 异步架构
- 基于asyncio和httpx的异步实现
- 支持并发请求处理
- 非阻塞IO操作

### 2. 分页支持
- 大部分列表操作支持分页
- offset和count参数控制
- 内存友好的大数据处理

### 3. 错误处理
- 分层错误处理机制
- 详细的错误信息返回
- 完整的异常日志记录

## 开发信息
- **作者**: ZinjaCoder
- **项目地址**: https://github.com/zinja-coder/jadx-ai-mcp
- **问题反馈**: https://github.com/zinja-coder/jadx-mcp-server/issues
- **许可证**: 详见LICENSE文件

## 总结
这是一个专业的Android应用程序逆向工程MCP服务器实现，提供了完整的JADX反编译器API接口。代码质量高，异常处理完善，适合用于安全研究和恶意软件分析。作为白帽工具，它为安全研究人员提供了强大的Android应用程序分析能力。