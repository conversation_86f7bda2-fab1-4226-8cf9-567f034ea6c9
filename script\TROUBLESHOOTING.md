# 微信Hook脚本故障排除指南

## 问题分析

根据您的运行结果，主要问题是**类名不匹配**。这是因为：

1. **微信版本差异**: 不同版本的微信使用不同的混淆类名
2. **代码混淆**: 微信使用了代码混淆技术，类名会随版本变化
3. **架构差异**: 不同的Android架构可能有不同的类结构

## 解决方案

### 🎯 推荐方案：使用动态Hook脚本

使用 `hook_msg_dynamic.js` 脚本，它会：
- 自动查找正确的类名
- 动态适配当前微信版本
- 无需手动修改类名

```bash
frida -U com.tencent.mm -l hook_msg_dynamic.js
```

### 🔍 调试方案：使用类查找脚本

如果动态脚本仍有问题，使用 `find_classes.js` 来分析：

```bash
frida -U com.tencent.mm -l find_classes.js
```

这个脚本会：
1. 分析当前微信的类结构
2. 找到包含消息字段的类
3. 测试Hook可行性
4. 提供正确的类名

## 当前问题详细分析

### ❌ 失败的Hook点

1. **消息对象类**: `p713qk.C127133t7` → 类不存在
2. **发送核心类**: `et0.C77023o` → 类不存在  
3. **Pipeline类**: `np4.C119851q`, `vp4.C149175c0` → 类不存在
4. **网络类**: `okhttp3.OkHttpClient` → 类不存在

### ✅ 成功的Hook点

1. **UI交互**: `com.tencent.mm.pluginsdk.ui.chat.ChatFooter` → 成功Hook
2. **输入框**: `com.tencent.mm.ui.widget.MMEditText` → 部分成功（方法重载问题）

## 手动修复步骤

如果需要手动修复，按以下步骤：

### 第一步：找到正确的消息类

运行类查找脚本后，查看输出中的消息类候选：

```
[1] com.example.NewMessageClass
    消息字段数: 8
    有setType: true
    有getContent: true
```

### 第二步：更新Hook脚本

将找到的类名替换到Hook脚本中：

```javascript
// 原来的代码
var MessageClass = Java.use("p713qk.C127133t7");

// 替换为新的类名
var MessageClass = Java.use("com.example.NewMessageClass");
```

### 第三步：测试Hook效果

发送一条微信消息，观察Hook输出。

## 常见错误及解决方法

### 1. ClassNotFoundException

**错误**: `java.lang.ClassNotFoundException: Didn't find class`

**原因**: 类名不正确或类未加载

**解决**: 
- 使用动态Hook脚本
- 运行类查找脚本找到正确类名
- 确保在发送消息时类已加载

### 2. 方法重载错误

**错误**: `has more than one overload, use .overload(<signature>)`

**原因**: 方法有多个重载版本

**解决**:
```javascript
// 错误的写法
EditTextClass.getText.implementation = function() { ... };

// 正确的写法
EditTextClass.getText.overload().implementation = function() { ... };
```

### 3. 方法不存在

**错误**: 方法调用失败

**原因**: 方法名在当前版本中不存在

**解决**: 
- 使用类查找脚本分析可用方法
- 尝试Hook类似功能的其他方法

## 版本适配指南

### 微信 8.0.x 版本
- 消息类通常以 `qk.` 开头
- 发送类通常以 `et0.` 开头

### 微信 8.1.x 版本  
- 类名可能有所变化
- 建议使用动态Hook脚本

### 微信 8.2.x 版本
- 混淆程度更高
- 必须使用动态查找方法

## 最佳实践

### 1. 分步调试
1. 先运行类查找脚本
2. 确认找到正确的类
3. 再运行Hook脚本

### 2. 渐进式Hook
1. 先Hook UI交互（成功率高）
2. 再Hook消息对象
3. 最后Hook网络和数据库

### 3. 日志分析
- 仔细查看Frida输出
- 关注成功的Hook点
- 分析失败原因

## 当前状态总结

根据您的运行结果：

✅ **已成功Hook**:
- ChatFooter发送按钮点击
- 可以监控发送按钮的点击事件
- 可以获取调用堆栈

❌ **需要修复**:
- 消息对象的属性设置
- 核心发送方法
- 网络请求监控

## 下一步建议

1. **立即尝试**: 运行 `hook_msg_dynamic.js` 脚本
2. **如果仍有问题**: 运行 `find_classes.js` 分析类结构
3. **发送测试消息**: 在微信中发送消息观察Hook效果
4. **根据输出调整**: 基于分析结果优化Hook脚本

## 联系支持

如果问题仍然存在，请提供：
1. 微信版本号
2. Android版本
3. 设备型号
4. 完整的Frida输出日志

这样可以帮助进一步诊断和解决问题。
