/**
 * 微信消息发送动态Hook脚本
 * 自动查找正确的类名，适配不同版本的微信
 * 基于实际运行环境动态适配
 */

console.log("🚀 微信消息发送动态Hook脚本启动");
console.log("📱 正在分析当前微信版本的类结构...");

Java.perform(function() {
    console.log("[+] Java环境初始化完成");
    
    var messageCount = 0;
    var foundClasses = {};
    
    // 工具函数：安全地尝试Hook方法
    function safeHook(clazz, methodName, hookFunction, description) {
        try {
            if (clazz[methodName]) {
                clazz[methodName].implementation = hookFunction;
                console.log(`[✅] ${description} Hook成功`);
                return true;
            }
        } catch (e) {
            console.log(`[❌] ${description} Hook失败: ${e.message}`);
        }
        return false;
    }
    
    // 工具函数：查找包含特定字段的类
    function findClassesWithFields(fieldNames) {
        var results = [];
        Java.enumerateLoadedClasses({
            onMatch: function(className) {
                try {
                    var clazz = Java.use(className);
                    var hasFields = fieldNames.some(function(fieldName) {
                        try {
                            var fields = clazz.class.getDeclaredFields();
                            for (var i = 0; i < fields.length; i++) {
                                if (fields[i].getName().includes(fieldName)) {
                                    return true;
                                }
                            }
                        } catch (e) {}
                        return false;
                    });
                    if (hasFields) {
                        results.push(className);
                    }
                } catch (e) {}
            },
            onComplete: function() {}
        });
        return results;
    }
    
    // 第一步：查找消息相关的类
    console.log("\n[*] 第一步：查找消息相关类...");
    
    // 查找包含消息字段的类
    var messageFieldClasses = findClassesWithFields(["content", "talker", "createTime", "msgId"]);
    console.log(`[+] 找到 ${messageFieldClasses.length} 个包含消息字段的类`);
    
    // 尝试Hook找到的消息类
    for (var i = 0; i < Math.min(messageFieldClasses.length, 10); i++) {
        try {
            var className = messageFieldClasses[i];
            var clazz = Java.use(className);
            
            // 检查是否有setType方法
            if (clazz.setType) {
                console.log(`\n[🎯] 尝试Hook消息类: ${className}`);
                
                // Hook setType方法
                safeHook(clazz, "setType", function(type) {
                    messageCount++;
                    var typeDesc = {
                        1: "文本", 3: "图片", 34: "语音", 
                        43: "视频", 47: "表情", 49: "链接"
                    }[type] || `未知(${type})`;
                    
                    console.log(`\n📝 [消息 #${messageCount}] 类型设置: ${typeDesc}`);
                    console.log(`时间: ${new Date().toLocaleString('zh-CN')}`);
                    console.log(`类名: ${className}`);
                    
                    return this.setType(type);
                }, `消息类型设置 (${className})`);
                
                // 尝试Hook其他可能的方法
                var methods = clazz.class.getDeclaredMethods();
                for (var j = 0; j < methods.length; j++) {
                    var methodName = methods[j].getName();
                    
                    // Hook可能的内容设置方法
                    if (methodName.length <= 3 && methodName.match(/^[a-z]\d+$/)) {
                        try {
                            var originalMethod = clazz[methodName];
                            if (originalMethod && originalMethod.argumentTypes && originalMethod.argumentTypes.length === 1) {
                                var argType = originalMethod.argumentTypes[0].className;
                                if (argType === "java.lang.String") {
                                    clazz[methodName].implementation = function(content) {
                                        if (content && content.length > 0 && content.length < 1000) {
                                            console.log(`📝 [内容设置] ${methodName}: ${content}`);
                                        }
                                        return this[methodName](content);
                                    };
                                    console.log(`[+] Hook字符串方法: ${methodName}`);
                                }
                            }
                        } catch (e) {}
                    }
                }
                
                foundClasses.messageClass = className;
                break;
            }
        } catch (e) {
            continue;
        }
    }
    
    // 第二步：Hook UI交互（这个已经成功了）
    console.log("\n[*] 第二步：Hook UI交互...");
    try {
        var ChatFooterClass = Java.use("com.tencent.mm.pluginsdk.ui.chat.ChatFooter");
        console.log("[+] ChatFooter类已成功Hook");
        
        // 增强现有的Hook
        ChatFooterClass.A.implementation = function() {
            console.log("\n🎯 [UI事件] 发送按钮被点击");
            console.log(`时间: ${new Date().toLocaleString('zh-CN')}`);
            
            // 尝试获取输入框内容
            try {
                var editText = this.getEditText ? this.getEditText() : null;
                if (editText && editText.getText) {
                    var content = editText.getText().toString();
                    if (content && content.length > 0) {
                        console.log(`📝 输入框内容: ${content}`);
                    }
                }
            } catch (e) {}
            
            var result = this.A.apply(this, arguments);
            console.log("[+] 发送按钮处理完成");
            return result;
        };
        
    } catch (e) {
        console.log(`[❌] Hook UI交互失败: ${e}`);
    }
    
    // 第三步：Hook EditText
    console.log("\n[*] 第三步：Hook输入框...");
    try {
        var EditTextClass = Java.use("com.tencent.mm.ui.widget.MMEditText");
        
        // Hook getText方法（处理重载）
        EditTextClass.getText.overload().implementation = function() {
            var text = this.getText();
            if (text && text.toString().length > 0) {
                console.log(`📝 [输入框] 获取内容: ${text.toString()}`);
            }
            return text;
        };
        
        console.log("[+] 输入框Hook设置完成");
        
    } catch (e) {
        console.log(`[❌] Hook输入框失败: ${e}`);
    }
    
    // 第四步：通用方法Hook
    console.log("\n[*] 第四步：设置通用Hook...");
    
    // Hook所有可能的发送相关方法
    Java.enumerateLoadedClasses({
        onMatch: function(className) {
            if (className.includes("send") || className.includes("Send") || 
                className.includes("msg") || className.includes("Msg")) {
                try {
                    var clazz = Java.use(className);
                    var methods = clazz.class.getDeclaredMethods();
                    
                    for (var i = 0; i < methods.length; i++) {
                        var methodName = methods[i].getName();
                        if (methodName.includes("send") || methodName.includes("Send")) {
                            try {
                                clazz[methodName].implementation = function() {
                                    console.log(`\n🚀 [发送方法] ${className}.${methodName} 被调用`);
                                    console.log(`参数数量: ${arguments.length}`);
                                    return this[methodName].apply(this, arguments);
                                };
                                console.log(`[+] Hook发送方法: ${className}.${methodName}`);
                            } catch (e) {}
                        }
                    }
                } catch (e) {}
            }
        },
        onComplete: function() {}
    });
    
    // 定期统计
    setInterval(function() {
        if (messageCount > 0) {
            console.log(`\n📊 统计: 已处理 ${messageCount} 条消息`);
        }
    }, 60000);
    
    console.log("\n" + "=".repeat(60));
    console.log("🎉 动态Hook脚本初始化完成！");
    console.log("📱 已适配当前微信版本");
    console.log("🔍 现在可以发送消息来测试Hook效果");
    console.log("=".repeat(60));
});
