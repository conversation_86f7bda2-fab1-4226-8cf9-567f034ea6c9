#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主GUI窗口
包含设备管理、连接控制和日志显示功能
"""

import sys
import os
import time
import re
import queue
from collections import deque
import datetime
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QSplitter, QTabWidget, QTableWidget, QTableWidgetItem,
                             QPushButton, QLabel, QTextEdit, QComboBox, QLineEdit,
                             QGroupBox, QProgressBar, QStatusBar, QMessageBox,
                             QHeaderView, QMenu, QAction, QMenuBar, QCheckBox,
                             QGridLayout) # Added QGridLayout
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QObject, QMetaObject, Q_ARG
from PyQt5.QtGui import <PERSON><PERSON>ont, QIcon, QPixmap, QColor, QTextCursor, QCursor
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QSettings # Added QSettings

# 添加utils路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.adb_manager import ADBManager, DeviceInfo
from utils.scrcpy_manager import ScrcpyManager
from gui.file_drop_widget import FileDropWidget

class DeviceRefreshThread(QThread):
    """设备刷新线程"""
    devices_updated = pyqtSignal(list)
    
    def __init__(self, adb_manager):
        super().__init__()
        self.adb_manager = adb_manager
        self.running = True
    
    def run(self):
        while self.running:
            devices = self.adb_manager.get_devices()
            self.devices_updated.emit(devices)
            self.msleep(2000)  # 每2秒刷新一次
    
    def stop(self):
        self.running = False

# 移除WiFi扫描线程类，不再需要

class LogItem:
    """日志项类，用于解析和存储logcat日志"""
    
    # 日志级别常量
    VERBOSE = 0
    DEBUG = 1
    INFO = 2
    WARNING = 3
    ERROR = 4
    FATAL = 5
    
    # 日志级别名称映射
    LEVEL_NAMES = {
        VERBOSE: "Verbose",
        DEBUG: "Debug",
        INFO: "Info",
        WARNING: "Warning",
        ERROR: "Error",
        FATAL: "Fatal"
    }
    
    # 日志级别颜色映射
    LEVEL_COLORS = {
        VERBOSE: QColor(128, 128, 128),  # 灰色
        DEBUG: QColor(0, 0, 255),        # 蓝色
        INFO: QColor(0, 128, 0),         # 绿色
        WARNING: QColor(255, 165, 0),    # 橙色
        ERROR: QColor(255, 0, 0),        # 红色
        FATAL: QColor(128, 0, 128)       # 紫色
    }
    
    def __init__(self, timestamp, level, tag, message):
        self.timestamp = timestamp
        self.level = level
        self.tag = tag
        self.message = message
    
    @staticmethod
    def parse_log(line):
        """解析日志行"""
        try:
            # 首先检查是否是已经格式化的日志（例如从UI复制的）
            # 例如: 12:40:59.433 | Info | 未知 | 07-08 12:39:19.695 18575 18664 E cent.mm:support: xiaojianbang ArtMethod::RegisterNative...
            pattern_formatted = r'^(\d{2}:\d{2}:\d{2}\.\d{3})\s*\|\s*(\w+)\s*\|\s*([^|]+)\s*\|\s*(.+)$'
            match_formatted = re.match(pattern_formatted, line)
            if match_formatted:
                timestamp, level_name, tag, original_message = match_formatted.groups()
                
                # 将级别名称转换为级别代码
                level_map = {
                    'Verbose': LogItem.VERBOSE,
                    'Debug': LogItem.DEBUG,
                    'Info': LogItem.INFO,
                    'Warning': LogItem.WARNING,
                    'Error': LogItem.ERROR,
                    'Fatal': LogItem.FATAL
                }
                level = level_map.get(level_name, LogItem.INFO)
                
                # 尝试从原始消息中提取更准确的标签和消息
                # 格式示例: 07-08 12:39:19.695 18575 18664 E cent.mm:support: xiaojianbang ArtMethod::RegisterNative...
                logcat_pattern = r'^(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})\s+(\d+)\s+(\d+)\s+([VDIWE])\s+([^\s:]+(?:\.[^\s:]+)*(?::[^\s:]+)?):\s+(.+)$'
                logcat_match = re.match(logcat_pattern, original_message)
                if logcat_match:
                    _, _, _, _, real_tag, real_message = logcat_match.groups()
                    return LogItem(timestamp, level, real_tag.strip(), real_message.strip())
                
                return LogItem(timestamp, level, tag.strip(), original_message.strip())
            
            # 尝试匹配标准logcat格式
            # 格式示例: 07-08 12:39:19.695 18575 18664 E cent.mm:support: xiaojianbang ArtMethod::RegisterNative...
            # 标签可能包含点(.)和冒号(:)，如 cent.mm:support
            pattern = r'^(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})\s+(\d+)\s+(\d+)\s+([VDIWE])\s+([^\s:]+(?:\.[^\s:]+)*(?::[^\s:]+)?):\s+(.+)$'
            match = re.match(pattern, line)
            
            if match:
                full_timestamp, pid, tid, level_char, tag, message = match.groups()
                
                # 提取时分秒部分，去掉月日
                timestamp_parts = full_timestamp.split(' ')
                if len(timestamp_parts) > 1:
                    timestamp = timestamp_parts[1]  # 只保留时分秒部分
                else:
                    timestamp = full_timestamp
                
                # 将日志级别字符转换为级别代码
                level_map = {'V': LogItem.VERBOSE, 'D': LogItem.DEBUG, 'I': LogItem.INFO, 
                            'W': LogItem.WARNING, 'E': LogItem.ERROR, 'F': LogItem.FATAL}
                
                level = level_map.get(level_char, LogItem.INFO)
                
                return LogItem(timestamp, level, tag, message.strip())
            else:
                # 尝试匹配简化的logcat格式（没有PID和TID）
                # 标签可能包含点(.)和冒号(:)，如 cent.mm:support
                pattern2 = r'^(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})\s+([VDIWE])\s+([^\s:]+(?:\.[^\s:]+)*(?::[^\s:]+)?):\s+(.+)$'
                match2 = re.match(pattern2, line)
                if match2:
                    full_timestamp, level_char, tag, message = match2.groups()
                    
                    # 提取时分秒部分，去掉月日
                    timestamp_parts = full_timestamp.split(' ')
                    if len(timestamp_parts) > 1:
                        timestamp = timestamp_parts[1]  # 只保留时分秒部分
                    else:
                        timestamp = full_timestamp
                    
                    level_map = {'V': LogItem.VERBOSE, 'D': LogItem.DEBUG, 'I': LogItem.INFO, 
                                'W': LogItem.WARNING, 'E': LogItem.ERROR, 'F': LogItem.FATAL}
                    level = level_map.get(level_char, LogItem.INFO)
                    
                    return LogItem(timestamp, level, tag, message.strip())
                
                # 匹配形如 [Level/Tag] Message 的格式
                pattern3 = r'^\[([VDIWE])/([^]]+)\]\s+(.+)$'
                match3 = re.match(pattern3, line)
                if match3:
                    level_char, tag, message = match3.groups()
                    level_map = {'V': LogItem.VERBOSE, 'D': LogItem.DEBUG, 'I': LogItem.INFO, 
                                'W': LogItem.WARNING, 'E': LogItem.ERROR, 'F': LogItem.FATAL}
                    level = level_map.get(level_char, LogItem.INFO)
                    # 只使用时分秒
                    now = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    return LogItem(now, level, tag.strip(), message.strip())
                
                # 尝试匹配已经格式化的日志（例如从文件加载）
                pattern4 = r'^(\d{2}:\d{2}:\d{2}\.\d{3})\s+(\w+)\s+([^:]+):\s+(.+)$'
                match4 = re.match(pattern4, line)
                if match4:
                    timestamp, level_name, tag, message = match4.groups()
                    
                    # 将级别名称转换为级别代码
                    level_map = {
                        'Verbose': LogItem.VERBOSE,
                        'Debug': LogItem.DEBUG,
                        'Info': LogItem.INFO,
                        'Warning': LogItem.WARNING,
                        'Error': LogItem.ERROR,
                        'Fatal': LogItem.FATAL
                    }
                    level = level_map.get(level_name, LogItem.INFO)
                    
                    return LogItem(timestamp, level, tag.strip(), message.strip())
                
                # 如果仍然无法匹配，使用当前时间并将整行视为消息
                now = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                return LogItem(now, LogItem.INFO, "未知", line.strip())
                
        except Exception as e:
            # 解析失败时创建一个错误日志项
            now = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            return LogItem(now, LogItem.ERROR, "解析错误", f"无法解析日志行: {line[:100]}... (错误: {e})")
    
    def get_level_name(self):
        """获取日志级别名称"""
        return self.LEVEL_NAMES.get(self.level, "未知")
    
    def get_level_color(self):
        """获取日志级别颜色"""
        return self.LEVEL_COLORS.get(self.level, QColor(0, 0, 0))

class LogcatWidget(QWidget):
    """Logcat日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.log_queue = queue.Queue()
        self.log_cache = deque(maxlen=10000)  # 最多存储10000条日志
        self.filter_tag = ""
        self.filter_level = ""
        self.init_ui()
        
        # 启动定时器，定期更新表格
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.process_log_queue)
        self.update_timer.start(100)  # 每100ms更新一次
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 控制栏
        control_layout = QHBoxLayout()
        
        # 过滤器
        filter_layout = QGridLayout()
        
        # 标签过滤
        self.tag_filter = QLineEdit()
        self.tag_filter.setPlaceholderText("标签过滤 (例如: ActivityManager)")
        self.tag_filter.textChanged.connect(self.apply_filters)
        
        # 标签列表下拉框
        self.tag_combo = QComboBox()
        self.tag_combo.setEditable(True)
        self.tag_combo.setInsertPolicy(QComboBox.InsertAlphabetically)
        self.tag_combo.setMaxVisibleItems(20)
        self.tag_combo.currentTextChanged.connect(self.on_tag_selected)
        
        # 级别过滤
        self.level_filter = QComboBox()
        self.level_filter.addItems(["全部", "Verbose", "Debug", "Info", "Warning", "Error", "Fatal"])
        self.level_filter.currentTextChanged.connect(self.apply_filters)
        
        # 消息搜索
        self.search_filter = QLineEdit()
        self.search_filter.setPlaceholderText("搜索消息内容")
        self.search_filter.textChanged.connect(self.apply_filters)
        
        # 添加到过滤器布局
        filter_layout.addWidget(QLabel("标签:"), 0, 0)
        filter_layout.addWidget(self.tag_filter, 0, 1)
        filter_layout.addWidget(self.tag_combo, 0, 2)
        filter_layout.addWidget(QLabel("级别:"), 0, 3)
        filter_layout.addWidget(self.level_filter, 0, 4)
        filter_layout.addWidget(QLabel("搜索:"), 1, 0)
        filter_layout.addWidget(self.search_filter, 1, 1, 1, 4)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("清空日志")
        self.save_btn = QPushButton("保存日志")
        self.auto_scroll = QCheckBox("自动滚动")
        self.auto_scroll.setChecked(True)
        
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.auto_scroll)
        button_layout.addStretch()
        
        # 添加到控制布局
        control_layout.addLayout(filter_layout)
        
        # 日志表格
        self.log_table = QTableWidget(0, 4)
        self.log_table.setHorizontalHeaderLabels(["时间", "级别", "标签", "消息"])
        
        # 设置表格属性
        self.log_table.setAlternatingRowColors(True)
        self.log_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.log_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.log_table.setSortingEnabled(True)
        self.log_table.setColumnWidth(0, 100)  # 时间列宽度，减小宽度
        self.log_table.setColumnWidth(1, 70)   # 级别列宽度，减小宽度
        self.log_table.setColumnWidth(2, 120)  # 标签列宽度，减小宽度
        
        # 设置表格自适应占满宽度
        self.log_table.horizontalHeader().setStretchLastSection(True)  # 最后一列自动拉伸填充剩余空间
        self.log_table.horizontalHeader().setSectionsMovable(True)  # 允许拖动列
        self.log_table.horizontalHeader().setHighlightSections(True)  # 高亮表头
        self.log_table.verticalHeader().setVisible(False)  # 隐藏垂直表头
        
        # 设置列宽可调整
        self.log_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Interactive)
        self.log_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)
        self.log_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)
        self.log_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)  # 消息列自适应拉伸
        
        # 设置最小列宽
        self.log_table.horizontalHeader().setMinimumSectionSize(60)  # 减小最小列宽
        
        # 设置样式
        self.log_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                font-family: Consolas, Monospace;
                font-size: 9pt;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
        
        # 添加右键菜单
        self.log_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.log_table.customContextMenuRequested.connect(self.show_context_menu)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.log_count_label = QLabel("日志数量: 0")
        status_layout.addWidget(self.log_count_label)
        status_layout.addStretch()
        
        # 添加到主布局
        layout.addLayout(control_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.log_table)
        layout.addLayout(status_layout)
        
        # 连接信号
        self.clear_btn.clicked.connect(self.clear_logs)
        self.save_btn.clicked.connect(self.save_logs)
        
        # 初始化标签集合
        self.tags = set()
        
        self.setLayout(layout)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu()
        
        # 复制所选行
        copy_action = QAction("复制所选行", self)
        copy_action.triggered.connect(self.copy_selected_rows)
        menu.addAction(copy_action)
        
        # 复制单元格
        if self.log_table.selectedItems():
            copy_cell_action = QAction("复制单元格内容", self)
            copy_cell_action.triggered.connect(self.copy_cell_content)
            menu.addAction(copy_cell_action)
        
        menu.addSeparator()
        
        # 导出所选行
        export_selected_action = QAction("导出所选行", self)
        export_selected_action.triggered.connect(self.export_selected_rows)
        menu.addAction(export_selected_action)
        
        menu.exec_(self.log_table.mapToGlobal(position))
    
    def copy_selected_rows(self):
        """复制所选行到剪贴板"""
        selected_rows = set()
        for item in self.log_table.selectedItems():
            selected_rows.add(item.row())
        
        text = ""
        for row in sorted(selected_rows):
            row_text = []
            for col in range(self.log_table.columnCount()):
                item = self.log_table.item(row, col)
                if item:
                    text_value = item.text()
                    row_text.append(text_value)
            text += " | ".join(row_text) + "\n"
        
        QApplication.clipboard().setText(text)
    
    def copy_cell_content(self):
        """复制单元格内容到剪贴板"""
        if len(self.log_table.selectedItems()) == 1:
            item = self.log_table.selectedItems()[0]
            QApplication.clipboard().setText(item.text())
    
    def export_selected_rows(self):
        """导出所选行到文件"""
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出日志", "selected_logs.txt", "文本文件 (*.txt);;所有文件 (*)")
        
        if filename:
            selected_rows = set()
            for item in self.log_table.selectedItems():
                selected_rows.add(item.row())
            
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    for row in sorted(selected_rows):
                        row_text = []
                        for col in range(self.log_table.columnCount()):
                            item = self.log_table.item(row, col)
                            if item:
                                text = item.text()
                                row_text.append(text)
                        f.write(" | ".join(row_text) + "\n")
                QMessageBox.information(self, "成功", f"所选日志已导出到: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出日志失败: {e}")
    
    def add_log_line(self, line):
        """添加日志行到队列"""
        # 将日志行放入队列，稍后由定时器处理
        self.log_queue.put(line)
    
    def process_log_queue(self):
        """处理日志队列"""
        # 一次最多处理100条日志，避免UI卡顿
        max_batch = 100
        batch_count = 0
        
        # 暂时禁用排序和UI更新，提高性能
        was_sorting_enabled = self.log_table.isSortingEnabled()
        self.log_table.setSortingEnabled(False)
        self.log_table.setUpdatesEnabled(False)
        
        try:
            while batch_count < max_batch and not self.log_queue.empty():
                line = self.log_queue.get()
                log_item = LogItem.parse_log(line)
                self.log_cache.append(log_item)
                
                # 更新标签集合
                if log_item.tag and log_item.tag != "未知" and log_item.tag != "解析错误":
                    if log_item.tag not in self.tags:
                        self.tags.add(log_item.tag)
                        self.tag_combo.addItem(log_item.tag)
                
                # 检查是否符合过滤条件
                if self.should_display(log_item):
                    self.add_log_to_table(log_item)
                
                batch_count += 1
        finally:
            # 恢复排序和UI更新
            self.log_table.setSortingEnabled(was_sorting_enabled)
            self.log_table.setUpdatesEnabled(True)
            
            # 更新日志计数
            self.log_count_label.setText(f"日志数量: {self.log_table.rowCount()}")
            
            # 如果启用了自动滚动，则滚动到底部
            if self.auto_scroll.isChecked() and batch_count > 0:
                self.log_table.scrollToBottom()
    
    def add_log_to_table(self, log_item):
        """添加日志项到表格"""
        row = self.log_table.rowCount()
        self.log_table.insertRow(row)
        
        # 时间列
        time_item = QTableWidgetItem(log_item.timestamp)
        time_item.setData(Qt.UserRole, log_item)
        self.log_table.setItem(row, 0, time_item)
        
        # 级别列
        level_item = QTableWidgetItem(log_item.get_level_name())
        level_item.setForeground(log_item.get_level_color())
        self.log_table.setItem(row, 1, level_item)
        
        # 标签列
        tag_item = QTableWidgetItem(log_item.tag)
        self.log_table.setItem(row, 2, tag_item)
        
        # 消息列
        message_item = QTableWidgetItem(log_item.message)
        message_item.setForeground(log_item.get_level_color())
        self.log_table.setItem(row, 3, message_item)
        
        # 根据日志级别设置行背景色
        if log_item.level == LogItem.ERROR or log_item.level == LogItem.FATAL:
            for col in range(4):
                item = self.log_table.item(row, col)
                if item:
                    item.setBackground(QColor(255, 240, 240))  # 浅红色背景
    
    def should_display(self, log_item):
        """检查日志项是否应该显示"""
        # 检查标签过滤器
        if self.filter_tag and self.filter_tag.lower() not in log_item.tag.lower():
            return False
        
        # 检查级别过滤器
        if self.filter_level and self.filter_level != "全部":
            level_map = {
                "Verbose": "V",
                "Debug": "D",
                "Info": "I",
                "Warning": "W",
                "Error": "E",
                "Fatal": "F"
            }
            
            # 获取当前过滤级别对应的代码
            filter_code = level_map.get(self.filter_level)
            
            # 获取所有比当前过滤级别更高或相等的级别代码
            level_codes = "VDIWEF"
            level_index = level_codes.find(filter_code)
            
            # 如果日志级别低于过滤级别，不显示
            if level_codes.find(log_item.get_level_name()[0]) < level_index:
                return False
        
        # 检查消息搜索过滤器
        if self.search_filter.text():
            if self.search_filter.text().lower() not in log_item.message.lower():
                return False
        
        return True
    
    def apply_filters(self):
        """应用过滤器"""
        self.filter_tag = self.tag_filter.text().strip()
        self.filter_level = self.level_filter.currentText()
        
        # 清空表格
        self.log_table.clearContents()
        self.log_table.setRowCount(0)
        
        # 暂时禁用排序和UI更新，提高性能
        was_sorting_enabled = self.log_table.isSortingEnabled()
        self.log_table.setSortingEnabled(False)
        self.log_table.setUpdatesEnabled(False)
        
        try:
            # 重新添加符合条件的日志
            displayed_count = 0
            for log_item in self.log_cache:
                if self.should_display(log_item):
                    self.add_log_to_table(log_item)
                    displayed_count += 1
                    
                    # 限制最大显示数量，避免UI卡顿
                    if displayed_count >= 5000:
                        break
        finally:
            # 恢复排序和UI更新
            self.log_table.setSortingEnabled(was_sorting_enabled)
            self.log_table.setUpdatesEnabled(True)
            
            # 更新日志计数
            self.log_count_label.setText(f"日志数量: {self.log_table.rowCount()} / {len(self.log_cache)}")
            
            # 如果有日志，滚动到底部
            if self.log_table.rowCount() > 0 and self.auto_scroll.isChecked():
                self.log_table.scrollToBottom()
    
    def on_tag_selected(self, text):
        """标签下拉框选择事件"""
        self.filter_tag = text.strip()
        self.apply_filters()
    
    def clear_logs(self):
        """清空日志"""
        self.log_table.clearContents()
        self.log_table.setRowCount(0)
        self.log_cache.clear()
    
    def save_logs(self):
        """保存日志到文件"""
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存日志", "logcat.txt", "文本文件 (*.txt);;所有文件 (*)")
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    # 保存所有日志，按照原始顺序
                    for log_item in self.log_cache:
                        # 格式化日志行，使用与UI显示相同的格式
                        log_line = f"{log_item.timestamp} | {log_item.get_level_name()} | {log_item.tag} | {log_item.message}"
                        f.write(f"{log_line}\n")
                
                QMessageBox.information(self, "成功", f"日志已保存到: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存日志失败: {e}")

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Android设备管理工具")
        self.resize(1200, 800)
        
        # 初始化属性
        self.current_device = None
        self.adb_manager = None
        self.device_refresh_thread = None
        self.wifi_scan_thread = None
        self.screen_window = None
        self.scrcpy_process = None
        self.scrcpy_available = False
        
        # 功能库文件状态
        self.lib_files_status = {
            "libcall": False,
            "libcapture": False,
            "libtrace": False
        }
        
        # 初始化系统日志列表
        self.system_logs = []
        
        # 初始化UI
        self.init_ui()
        
        # 初始化ADB
        self.init_adb()
        
        # 初始化scrcpy
        self.scrcpy_manager = ScrcpyManager()
        
        self.start_device_refresh()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("ADB工具 - 设备管理与日志查看")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 使用水平分割器
        main_layout = QHBoxLayout()
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧面板 - 设备管理
        left_panel = self.create_device_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧面板 - 文件传输和日志
        right_panel = self.create_tools_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例 [设备管理:工具面板] = [400:800]
        main_splitter.setSizes([400, 800])
        
        main_layout.addWidget(main_splitter)
        central_widget.setLayout(main_layout)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()
        
        # 文件菜单
        file_menu = menu_bar.addMenu("文件")
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设备菜单
        device_menu = menu_bar.addMenu("设备")
        
        refresh_action = QAction("刷新设备", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_devices)
        device_menu.addAction(refresh_action)
        
        # 工具菜单
        tools_menu = menu_bar.addMenu("工具")
        
        screenshot_action = QAction("截图", self)
        screenshot_action.setShortcut("F12")
        screenshot_action.triggered.connect(self.take_screenshot)
        tools_menu.addAction(screenshot_action)
        
        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_device_panel(self):
        """创建设备管理面板"""
        device_panel = QWidget()
        layout = QVBoxLayout()
        
        # 设备列表组
        device_group = QGroupBox("设备列表")
        device_layout = QVBoxLayout()
        
        # 设备表格
        self.device_table = QTableWidget(0, 3)
        self.device_table.setHorizontalHeaderLabels(["序列号", "状态", "型号"])
        self.device_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.device_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.device_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.device_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.device_table.setSelectionMode(QTableWidget.SingleSelection)
        self.device_table.itemSelectionChanged.connect(self.on_device_selected)
        self.device_table.horizontalHeader().setStretchLastSection(True)  # 最后一列自动拉伸填充剩余空间
        
        # 设备控制按钮
        button_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新")
        self.connect_btn = QPushButton("连接")
        self.disconnect_btn = QPushButton("断开")
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.connect_btn)
        button_layout.addWidget(self.disconnect_btn)
        
        # 连接按钮信号
        self.refresh_btn.clicked.connect(self.refresh_devices)
        self.connect_btn.clicked.connect(self.connect_device)
        self.disconnect_btn.clicked.connect(self.disconnect_device)
        
        device_layout.addWidget(self.device_table)
        device_layout.addLayout(button_layout)
        device_group.setLayout(device_layout)
        
        # 设备信息组
        info_group = QGroupBox("设备信息")
        info_layout = QVBoxLayout()
        
        self.device_info_text = QTextEdit()
        self.device_info_text.setReadOnly(True)
        self.device_info_text.setFont(QFont("Consolas", 9))
        
        info_layout.addWidget(self.device_info_text)
        info_group.setLayout(info_layout)
        
        # 屏幕控制组
        screen_group = QGroupBox("屏幕控制")
        screen_layout = QVBoxLayout()
        
        button_layout2 = QHBoxLayout()
        self.start_capture_btn = QPushButton("启动scrcpy屏幕镜像")
        self.stop_capture_btn = QPushButton("停止scrcpy")
        
        self.start_capture_btn.setEnabled(False)
        self.stop_capture_btn.setEnabled(False)
        
        button_layout2.addWidget(self.start_capture_btn)
        button_layout2.addWidget(self.stop_capture_btn)
        
        # scrcpy状态标签
        status_layout = QHBoxLayout()
        self.scrcpy_status = QLabel("scrcpy: 未检测")
        self.scrcpy_status.setStyleSheet("color: #888888;")
        status_layout.addWidget(self.scrcpy_status)
        status_layout.addStretch()
        
        # 连接信号
        self.start_capture_btn.clicked.connect(self.start_screen_capture)
        self.stop_capture_btn.clicked.connect(self.stop_screen_capture)
        
        screen_layout.addLayout(button_layout2)
        screen_layout.addLayout(status_layout)
        screen_group.setLayout(screen_layout)
        
        # 添加到主布局
        layout.addWidget(device_group)
        layout.addWidget(info_group)
        layout.addWidget(screen_group)
        
        device_panel.setLayout(layout)
        
        # 检查scrcpy可用性
        QTimer.singleShot(1000, self.check_scrcpy_availability)
        
        return device_panel
    
    def create_tools_panel(self):
        """创建工具面板"""
        tools_panel = QWidget()
        layout = QVBoxLayout()
        
        # 使用选项卡组织不同功能
        tab_widget = QTabWidget()
        
        # 文件传输选项卡
        file_tab = QWidget()
        file_layout = QVBoxLayout()
        
        # 创建文件拖放区域
        self.file_drop_widget = FileDropWidget()
        self.file_drop_widget.file_dropped.connect(self.on_file_dropped)
        
        file_layout.addWidget(self.file_drop_widget)
        file_tab.setLayout(file_layout)
        
        # Logcat选项卡
        logcat_tab = QWidget()
        logcat_layout = QVBoxLayout()
        
        # 创建Logcat控制区域
        logcat_control = QHBoxLayout()
        
        self.start_logcat_btn = QPushButton("开始Logcat")
        self.stop_logcat_btn = QPushButton("停止Logcat")
        self.stop_logcat_btn.setEnabled(False)
        
        logcat_control.addWidget(self.start_logcat_btn)
        logcat_control.addWidget(self.stop_logcat_btn)
        logcat_control.addStretch()
        
        # 连接信号
        self.start_logcat_btn.clicked.connect(self.start_logcat)
        self.stop_logcat_btn.clicked.connect(self.stop_logcat)
        
        # 创建功能按钮区域
        function_group = QGroupBox("功能开关")
        function_layout = QGridLayout()
        
        # 函数参数跟踪按钮
        self.libcall_btn = QPushButton("函数参数跟踪")
        self.libcall_btn.setCheckable(True)
        self.libcall_btn.setEnabled(False)
        self.libcall_btn.clicked.connect(lambda checked: self.toggle_lib_function("libcall", checked))
        
        # 自动抓包按钮
        self.libcapture_btn = QPushButton("自动抓包")
        self.libcapture_btn.setCheckable(True)
        self.libcapture_btn.setEnabled(False)
        self.libcapture_btn.clicked.connect(lambda checked: self.toggle_lib_function("libcapture", checked))
        
        # 函数跟踪按钮
        self.libtrace_btn = QPushButton("函数跟踪")
        self.libtrace_btn.setCheckable(True)
        self.libtrace_btn.setEnabled(False)
        self.libtrace_btn.clicked.connect(lambda checked: self.toggle_lib_function("libtrace", checked))
        
        # 添加按钮到布局
        function_layout.addWidget(QLabel("功能开关:"), 0, 0)
        function_layout.addWidget(self.libcall_btn, 0, 1)
        function_layout.addWidget(self.libcapture_btn, 0, 2)
        function_layout.addWidget(self.libtrace_btn, 0, 3)
        
        # 设置按钮样式
        button_style = """
            QPushButton {
                padding: 5px 10px;
                border-radius: 4px;
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
            }
            QPushButton:checked {
                background-color: #4CAF50;
                color: white;
                border: 1px solid #388E3C;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #a0a0a0;
                border: 1px solid #c0c0c0;
            }
        """
        self.libcall_btn.setStyleSheet(button_style)
        self.libcapture_btn.setStyleSheet(button_style)
        self.libtrace_btn.setStyleSheet(button_style)
        
        function_group.setLayout(function_layout)
        
        # 创建Logcat显示区域
        self.logcat_widget = LogcatWidget()
        
        # 添加到logcat布局
        logcat_layout.addLayout(logcat_control)
        logcat_layout.addWidget(function_group)
        logcat_layout.addWidget(self.logcat_widget)
        
        logcat_tab.setLayout(logcat_layout)
        
        # 创建Frida选项卡
        frida_tab = QWidget()
        frida_layout = QVBoxLayout()
        
        # Frida服务器控制区域
        frida_server_group = QGroupBox("Frida服务器")
        frida_server_layout = QGridLayout()
        
        # Frida版本选择
        frida_server_layout.addWidget(QLabel("Frida版本:"), 0, 0)
        self.frida_version_combo = QComboBox()
        self.frida_version_combo.addItems(["16.2.1", "16.1.4", "16.0.19", "15.2.2", "15.1.28"])
        self.frida_version_combo.setEditable(True)
        frida_server_layout.addWidget(self.frida_version_combo, 0, 1)
        
        # 安装Frida服务器按钮
        self.install_frida_btn = QPushButton("安装Frida服务器")
        self.install_frida_btn.clicked.connect(self.install_frida_server)
        frida_server_layout.addWidget(self.install_frida_btn, 0, 2)
        
        # 启动/停止Frida服务器按钮
        self.start_frida_btn = QPushButton("启动Frida服务器")
        self.start_frida_btn.clicked.connect(self.start_frida_server)
        frida_server_layout.addWidget(self.start_frida_btn, 1, 0)
        
        self.stop_frida_btn = QPushButton("停止Frida服务器")
        self.stop_frida_btn.clicked.connect(self.stop_frida_server)
        self.stop_frida_btn.setEnabled(False)
        frida_server_layout.addWidget(self.stop_frida_btn, 1, 1)
        
        # 检查Frida状态按钮
        self.check_frida_btn = QPushButton("检查Frida状态")
        self.check_frida_btn.clicked.connect(self.check_frida_status)
        frida_server_layout.addWidget(self.check_frida_btn, 1, 2)
        
        frida_server_group.setLayout(frida_server_layout)
        
        # Frida脚本执行区域
        frida_script_group = QGroupBox("Frida脚本执行")
        frida_script_layout = QGridLayout()
        
        # 应用包名输入
        frida_script_layout.addWidget(QLabel("应用包名:"), 0, 0)
        self.frida_package_input = QLineEdit()
        self.frida_package_input.setPlaceholderText("例如: com.example.app")
        frida_script_layout.addWidget(self.frida_package_input, 0, 1, 1, 2)
        
        # 刷新应用列表按钮
        self.refresh_apps_btn = QPushButton("刷新应用列表")
        self.refresh_apps_btn.clicked.connect(self.refresh_app_list)
        frida_script_layout.addWidget(self.refresh_apps_btn, 0, 3)
        
        # 应用列表
        frida_script_layout.addWidget(QLabel("选择应用:"), 1, 0)
        self.app_list_combo = QComboBox()
        self.app_list_combo.currentTextChanged.connect(self.on_app_selected)
        frida_script_layout.addWidget(self.app_list_combo, 1, 1, 1, 3)
        
        # 脚本选择
        frida_script_layout.addWidget(QLabel("脚本文件:"), 2, 0)
        self.frida_script_path = QLineEdit()
        self.frida_script_path.setReadOnly(True)
        frida_script_layout.addWidget(self.frida_script_path, 2, 1, 1, 2)
        
        # 选择脚本文件按钮
        self.select_script_btn = QPushButton("选择脚本")
        self.select_script_btn.clicked.connect(self.select_frida_script)
        frida_script_layout.addWidget(self.select_script_btn, 2, 3)
        
        # 不再使用示例脚本按钮
        # 留出空间
        frida_script_layout.addWidget(QWidget(), 3, 0)
        
        # 执行脚本按钮
        self.run_script_btn = QPushButton("执行脚本")
        self.run_script_btn.clicked.connect(self.run_frida_script)
        frida_script_layout.addWidget(self.run_script_btn, 3, 1)
        
        # 停止脚本按钮
        self.stop_script_btn = QPushButton("停止脚本")
        self.stop_script_btn.clicked.connect(self.stop_frida_script)
        self.stop_script_btn.setEnabled(False)
        frida_script_layout.addWidget(self.stop_script_btn, 3, 2)
        
        frida_script_group.setLayout(frida_script_layout)
        
        # 添加到Frida布局
        frida_layout.addWidget(frida_server_group)
        frida_layout.addWidget(frida_script_group)
        frida_layout.addStretch()
        
        frida_tab.setLayout(frida_layout)
        
        # 添加选项卡
        tab_widget.addTab(file_tab, "文件传输")
        tab_widget.addTab(logcat_tab, "Logcat日志")
        tab_widget.addTab(frida_tab, "Frida工具")
        
        # 一般日志区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        
        self.general_log = QTextEdit()
        self.general_log.setReadOnly(True)
        self.general_log.setFont(QFont("Consolas", 9))
        
        log_layout.addWidget(self.general_log)
        log_group.setLayout(log_layout)
        
        # 添加到主布局
        layout.addWidget(tab_widget, 3)  # 3:1的比例
        layout.addWidget(log_group, 1)
        
        tools_panel.setLayout(layout)
        return tools_panel
    
    def init_adb(self):
        """初始化ADB"""
        self.adb_manager = ADBManager()
        if self.adb_manager.initialize_adb():
            self.log_message("ADB初始化成功")
        else:
            self.log_message("ADB初始化失败，请确保ADB已安装并配置环境变量")
        
        # 设置文件拖放组件的ADB管理器
        self.file_drop_widget.set_adb_manager(self.adb_manager)
    
    def start_device_refresh(self):
        """启动设备刷新线程"""
        if self.device_refresh_thread:
            self.device_refresh_thread.stop()
            self.device_refresh_thread.wait()
        
        self.device_refresh_thread = DeviceRefreshThread(self.adb_manager)
        self.device_refresh_thread.devices_updated.connect(self.update_device_list)
        self.device_refresh_thread.start()
    
    def update_device_list(self, devices):
        """更新设备列表"""
        self.device_table.setRowCount(len(devices))
        
        for i, device in enumerate(devices):
            self.device_table.setItem(i, 0, QTableWidgetItem(device.serial))
            self.device_table.setItem(i, 1, QTableWidgetItem(device.status))
            self.device_table.setItem(i, 2, QTableWidgetItem(device.model))
            
            # 根据状态设置颜色
            if device.status == "device":
                color = QColor(144, 238, 144)  # 浅绿色
            elif device.status == "offline":
                color = QColor(255, 182, 193)  # 浅红色
            else:
                color = QColor(255, 255, 224)  # 浅黄色
            
            for j in range(3):
                item = self.device_table.item(i, j)
                if item:
                    item.setBackground(color)
    
    def on_device_selected(self):
        """设备选择事件"""
        selected_items = self.device_table.selectedItems()
        if not selected_items:
            return
        
        # 获取选中行的第一列（序列号）
        row = selected_items[0].row()
        serial = self.device_table.item(row, 0).text()
        
        # 更新当前设备
        self.current_device = serial
        
        # 更新设备信息
        self.update_device_info(serial)
        
        # 更新按钮状态
        status = self.device_table.item(row, 1).text()
        self.connect_btn.setEnabled(status != "device")
        self.disconnect_btn.setEnabled(status == "device")
        
        # 更新屏幕控制按钮状态
        self.start_capture_btn.setEnabled(status == "device")
        
        # 更新日志按钮状态
        self.start_logcat_btn.setEnabled(status == "device")
        self.stop_logcat_btn.setEnabled(False)
        
        # 更新文件拖放组件
        self.file_drop_widget.set_current_device(self.current_device)
        
        # 检查库文件状态
        if status == "device":
            self.check_lib_files_status()
        
        self.log_message(f"已选择设备: {serial}")
        
    def check_lib_files_status(self):
        """检查库文件状态"""
        if not self.current_device:
            return
            
        # 禁用按钮，等待检查完成
        self.libcall_btn.setEnabled(False)
        self.libcapture_btn.setEnabled(False)
        self.libtrace_btn.setEnabled(False)
        
        # 在单独的线程中执行检查，避免UI卡顿
        class CheckLibFilesThread(QThread):
            result_signal = pyqtSignal(dict)
            
            def __init__(self, adb_manager, serial):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                
            def run(self):
                result = self.adb_manager.check_lib_files(self.serial)
                self.result_signal.emit(result)
        
        # 创建并启动线程
        self.check_thread = CheckLibFilesThread(self.adb_manager, self.current_device)
        self.check_thread.result_signal.connect(self.update_lib_files_status)
        self.check_thread.start()
        
    def update_lib_files_status(self, status_dict):
        """更新库文件状态"""
        # 如果返回的字典为空，说明没有找到库文件
        if not status_dict:
            self.log_message(f"设备 {self.current_device} 未找到功能库文件")
            return
            
        # 更新状态
        self.lib_files_status = status_dict
        
        # 更新按钮状态
        self.libcall_btn.setChecked(status_dict.get("libcall", False))
        self.libcall_btn.setEnabled(True)
        
        self.libcapture_btn.setChecked(status_dict.get("libcapture", False))
        self.libcapture_btn.setEnabled(True)
        
        self.libtrace_btn.setChecked(status_dict.get("libtrace", False))
        self.libtrace_btn.setEnabled(True)
        
        # 记录日志
        self.log_message(f"检测到功能库文件状态: 函数参数跟踪({'开启' if status_dict.get('libcall', False) else '关闭'}), "
                         f"自动抓包({'开启' if status_dict.get('libcapture', False) else '关闭'}), "
                         f"函数跟踪({'开启' if status_dict.get('libtrace', False) else '关闭'})")
        
        # 添加初次检查时的提示信息
        self.log_message("初次检查时，如果发现带.so后缀的文件，已自动将其改为不带后缀（关闭状态）")
    
    def toggle_lib_function(self, lib_name, enable):
        """切换库文件功能状态
        
        Args:
            lib_name: 库文件名称（不含.so后缀）
            enable: 是否启用
        """
        if not self.current_device:
            return
            
        # 检查互斥条件
        if enable:
            # 如果要启用libcall，检查libtrace是否已启用
            if lib_name == "libcall" and self.lib_files_status.get("libtrace", False):
                QMessageBox.warning(self, "警告", "函数参数跟踪与函数跟踪不能同时开启")
                self.libcall_btn.setChecked(False)
                return
                
            # 如果要启用libtrace，检查libcall是否已启用
            if lib_name == "libtrace" and self.lib_files_status.get("libcall", False):
                QMessageBox.warning(self, "警告", "函数跟踪与函数参数跟踪不能同时开启")
                self.libtrace_btn.setChecked(False)
                return
        
        # 禁用按钮，避免重复操作
        self.libcall_btn.setEnabled(False)
        self.libcapture_btn.setEnabled(False)
        self.libtrace_btn.setEnabled(False)
        
        # 在单独的线程中执行操作，避免UI卡顿
        class ToggleLibThread(QThread):
            result_signal = pyqtSignal(str, bool, bool)
            
            def __init__(self, adb_manager, serial, lib_name, enable):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                self.lib_name = lib_name
                self.enable = enable
                
            def run(self):
                result = self.adb_manager.toggle_lib_file(self.serial, self.lib_name, self.enable)
                self.result_signal.emit(self.lib_name, self.enable, result)
        
        # 创建并启动线程
        self.toggle_thread = ToggleLibThread(self.adb_manager, self.current_device, lib_name, enable)
        self.toggle_thread.result_signal.connect(self.on_lib_function_toggled)
        self.toggle_thread.start()
        
    def on_lib_function_toggled(self, lib_name, enable, success):
        """库文件功能状态切换完成回调
        
        Args:
            lib_name: 库文件名称
            enable: 是否启用
            success: 操作是否成功
        """
        # 重新启用按钮
        self.libcall_btn.setEnabled(True)
        self.libcapture_btn.setEnabled(True)
        self.libtrace_btn.setEnabled(True)
        
        # 处理操作结果
        if success:
            # 更新状态
            self.lib_files_status[lib_name] = enable
            
            # 更新按钮状态
            if lib_name == "libcall":
                self.libcall_btn.setChecked(enable)
            elif lib_name == "libcapture":
                self.libcapture_btn.setChecked(enable)
            elif lib_name == "libtrace":
                self.libtrace_btn.setChecked(enable)
                
            # 记录日志
            status_text = "开启" if enable else "关闭"
            function_name = ""
            if lib_name == "libcall":
                function_name = "函数参数跟踪"
            elif lib_name == "libcapture":
                function_name = "自动抓包"
            elif lib_name == "libtrace":
                function_name = "函数跟踪"
                
            self.log_message(f"{function_name}功能{status_text}成功")
        else:
            # 恢复按钮状态
            if lib_name == "libcall":
                self.libcall_btn.setChecked(self.lib_files_status.get("libcall", False))
            elif lib_name == "libcapture":
                self.libcapture_btn.setChecked(self.lib_files_status.get("libcapture", False))
            elif lib_name == "libtrace":
                self.libtrace_btn.setChecked(self.lib_files_status.get("libtrace", False))
                
            # 记录日志
            status_text = "开启" if enable else "关闭"
            function_name = ""
            if lib_name == "libcall":
                function_name = "函数参数跟踪"
            elif lib_name == "libcapture":
                function_name = "自动抓包"
            elif lib_name == "libtrace":
                function_name = "函数跟踪"
                
            QMessageBox.warning(self, "操作失败", f"{function_name}功能{status_text}失败")
            self.log_message(f"{function_name}功能{status_text}失败")
    
    def update_device_info(self, serial):
        """更新设备信息显示"""
        try:
            info_text = f"设备序列号: {serial}\n"
            
            if serial in self.adb_manager.device_info_cache:
                device_info = self.adb_manager.device_info_cache[serial]
                info_text += f"品牌: {device_info.brand}\n"
                info_text += f"型号: {device_info.model}\n"
                info_text += f"Android版本: {device_info.android_version}\n"
                info_text += f"API级别: {device_info.api_level}\n"
                info_text += f"连接类型: {device_info.connection_type}\n"
            
            # 获取更多设备信息
            if serial in self.adb_manager.connected_devices:
                additional_info = self.adb_manager.execute_command(serial, "getprop ro.build.display.id")
                if additional_info:
                    info_text += f"系统版本: {additional_info.strip()}\n"
            
            self.device_info_text.setText(info_text)
            
        except Exception as e:
            self.device_info_text.setText(f"获取设备信息失败: {e}")
    
    def refresh_devices(self):
        """刷新设备列表"""
        self.log_message("正在刷新设备列表...")
        # 设备列表会通过线程自动更新
    
    def connect_device(self):
        """连接设备"""
        if not self.current_device:
            return
        
        self.log_message(f"正在连接设备: {self.current_device}")
        
        if self.adb_manager.connect_device(self.current_device):
            self.log_message(f"设备连接成功: {self.current_device}")
            
            # 设备连接成功后检查库文件状态
            self.check_lib_files_status()
        else:
            self.log_message(f"设备连接失败: {self.current_device}")
    
    def disconnect_device(self):
        """断开设备连接"""
        if not self.current_device:
            return
        
        self.log_message(f"正在断开设备: {self.current_device}")
        
        if self.adb_manager.disconnect_device(self.current_device):
            self.log_message(f"设备断开成功: {self.current_device}")
        else:
            self.log_message(f"设备断开失败: {self.current_device}")
    
    def connect_wifi_device(self):
        """连接WiFi设备"""
        ip_address = self.ip_input.text().strip()
        if not ip_address:
            QMessageBox.warning(self, "警告", "请输入IP地址")
            return
        
        # 确保有端口号
        if ":" not in ip_address:
            ip_address += ":5555"
        
        self.log_message(f"正在连接WiFi设备: {ip_address}")
        
        if self.adb_manager.connect_device(ip_address):
            self.log_message(f"WiFi设备连接成功: {ip_address}")
            self.ip_input.clear()
        else:
            self.log_message(f"WiFi设备连接失败: {ip_address}")
    
    def scan_wifi_devices(self):
        """扫描WiFi设备"""
        self.log_message("正在扫描WiFi设备...")
        self.scan_wifi_btn.setEnabled(False)
        
        if self.wifi_scan_thread and self.wifi_scan_thread.isRunning():
            return
        
        self.wifi_scan_thread = WiFiScanThread(self.adb_manager)
        self.wifi_scan_thread.wifi_devices_found.connect(self.on_wifi_devices_found)
        self.wifi_scan_thread.finished.connect(lambda: self.scan_wifi_btn.setEnabled(True))
        self.wifi_scan_thread.start()
    
    def on_wifi_devices_found(self, devices):
        """WiFi设备扫描完成"""
        self.log_message(f"发现 {len(devices)} 个WiFi设备")
        for device in devices:
            self.log_message(f"  - {device.serial}")
    
    def start_logcat(self):
        """开始Logcat"""
        if not self.current_device:
            return
        
        self.log_message(f"正在启动Logcat: {self.current_device}")
        
        # 获取过滤器设置
        filter_tag = self.logcat_widget.tag_filter.text().strip()
        filter_level_text = self.logcat_widget.level_filter.currentText()
        
        # 转换为adb识别的级别代码
        filter_level = ""
        if filter_level_text != "全部":
            level_map = {
                "Verbose": "V",
                "Debug": "D",
                "Info": "I",
                "Warning": "W",
                "Error": "E",
                "Fatal": "F"
            }
            filter_level = level_map.get(filter_level_text, "")
        
        # 使用线程安全的方式处理日志行
        def on_logcat_line(line):
            # 直接在这里使用信号发送日志行
            self.log_emitter.emit_log(line)
        
        # 初始化日志信号发射器
        if not hasattr(self, 'log_emitter'):
            class LogSignalEmitter(QObject):
                logcat_signal = pyqtSignal(str)
                
                def __init__(self, widget):
                    super().__init__()
                    self.widget = widget
                    # 直接连接到widget的方法
                    self.logcat_signal.connect(self.widget.add_log_line)
                
                def emit_log(self, line):
                    self.logcat_signal.emit(line)
            
            self.log_emitter = LogSignalEmitter(self.logcat_widget)
        
        if self.adb_manager.start_logcat(self.current_device, on_logcat_line, filter_tag, filter_level):
            self.log_message("Logcat启动成功")
            self.start_logcat_btn.setEnabled(False)
            self.stop_logcat_btn.setEnabled(True)
        else:
            self.log_message("Logcat启动失败")
    
    def stop_logcat(self):
        """停止Logcat"""
        if not self.current_device:
            return
        
        self.log_message(f"正在停止Logcat: {self.current_device}")
        self.adb_manager.stop_logcat(self.current_device)
        
        self.start_logcat_btn.setEnabled(True)
        self.stop_logcat_btn.setEnabled(False)
        self.log_message("Logcat已停止")
    
    def launch_android_studio_logcat(self):
        """启动Android Studio Logcat"""
        if not self.current_device:
            return
        
        self.log_message(f"正在启动Android Studio Logcat: {self.current_device}")
        
        if self.adb_manager.launch_android_studio_logcat(self.current_device):
            self.log_message("Android Studio Logcat启动成功")
        else:
            self.log_message("Android Studio Logcat启动失败，请检查Android Studio是否已安装")
    
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        # 添加到日志窗口
        self.logcat_widget.add_log_line(f"{datetime.now().strftime('%m-%d %H:%M:%S.000')} 0 0 I/System: {message}")
        
        self.general_log.append(formatted_message)
        
        # 自动滚动
        scrollbar = self.general_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "ADB工具 v1.0\n\n"
                         "功能特性:\n"
                         "• 设备管理和连接\n"
                         "• scrcpy高性能屏幕镜像\n"
                         "• Logcat日志查看\n"
                         "• 文件传输\n\n"
                         "作者: AI助手")
    
    def closeEvent(self, event):
        """关闭窗口事件"""
        # 停止设备刷新线程
        if self.device_refresh_thread:
            self.device_refresh_thread.stop()
            self.device_refresh_thread.wait()
        
        # 停止logcat
        if self.current_device:
            self.stop_logcat()
            
        # 停止屏幕捕获
        self.stop_screen_capture()
        
        # 停止Frida服务器
        if hasattr(self, 'current_device') and self.current_device:
            self.stop_frida_server()
            
        # 清理ADB资源
        if self.adb_manager:
            self.adb_manager.cleanup()
        
        # 保存窗口位置和大小
        settings = QSettings("XiaoJianBang", "AndroidTools")
        settings.setValue("geometry", self.saveGeometry())
        settings.setValue("windowState", self.saveState())
        
        # 保存分割器状态
        if hasattr(self, 'main_splitter'):
            settings.setValue("splitter", self.main_splitter.saveState())
        
        # 保存日志过滤器状态
        if hasattr(self, 'logcat_widget'):
            settings.setValue("tag_filter", self.logcat_widget.tag_filter.text())
            if hasattr(self.logcat_widget, 'level_combo'):
                settings.setValue("level_filter", self.logcat_widget.level_combo.currentText())
        
        event.accept()
    
    def clear_all_logs(self):
        """清空所有日志"""
        self.logcat_widget.clear_logs()
        self.general_log.clear()
    
    def show_screen_window(self):
        """显示屏幕窗口"""
        self.screen_window.show()
        self.screen_window.raise_()
        self.screen_window.activateWindow()
    
    def start_screen_capture(self):
        """启动scrcpy屏幕镜像"""
        if not self.current_device:
            return
            
        if not self.scrcpy_manager.is_scrcpy_available():
            self.log_message("scrcpy未安装，无法启动屏幕镜像")
            reply = QMessageBox.question(
                self, "下载scrcpy", 
                "未检测到scrcpy，是否下载scrcpy以启用高性能屏幕镜像？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.download_scrcpy()
            return
        
        # 使用scrcpy
        self.log_message("启动scrcpy屏幕镜像")
        
        # 启动scrcpy（不指定尺寸，让其自动根据设备屏幕大小计算）
        success = self.scrcpy_manager.start_scrcpy(
            self.current_device,
            window_title=f"ADB工具 - {self.current_device}",
            window_x=100,
            window_y=100,
            always_on_top=True
        )
        
        if success:
            self.log_message("scrcpy启动成功，享受流畅的屏幕镜像")
            self.start_capture_btn.setEnabled(False)
            self.stop_capture_btn.setEnabled(True)
        else:
            self.log_message("scrcpy启动失败")
    
    def stop_screen_capture(self):
        """停止scrcpy屏幕镜像"""
        # 停止scrcpy
        if hasattr(self, 'scrcpy_manager') and hasattr(self, 'current_device') and self.current_device and self.scrcpy_manager.is_running(self.current_device):
            self.log_message("停止scrcpy屏幕镜像")
            self.scrcpy_manager.stop_scrcpy(self.current_device)
        
        if hasattr(self, 'start_capture_btn'):
            self.start_capture_btn.setEnabled(True)
        if hasattr(self, 'stop_capture_btn'):
            self.stop_capture_btn.setEnabled(False)
    
    def on_screen_clicked(self, x, y):
        """屏幕点击事件"""
        if self.current_device and self.adb_manager:
            self.adb_manager.click_screen(self.current_device, x, y)
            self.log_message(f"点击屏幕: ({x}, {y})")
    
    def on_screen_swiped(self, x1, y1, x2, y2):
        """屏幕滑动事件"""
        if self.current_device and self.adb_manager:
            self.adb_manager.swipe_screen(self.current_device, x1, y1, x2, y2)
            self.log_message(f"滑动屏幕: ({x1}, {y1}) -> ({x2}, {y2})")
    
    def on_file_dropped(self, file_path):
        """文件拖放事件"""
        if not self.current_device or not self.adb_manager:
            return
        
        filename = os.path.basename(file_path)
        is_apk = file_path.lower().endswith('.apk')
        
        # 显示传输状态
        if is_apk:
            self.log_message(f"开始安装APK: {filename}")
            success = self.adb_manager.install_apk(self.current_device, file_path)
            if success:
                self.log_message(f"APK安装成功: {filename}")
            else:
                self.log_message(f"APK安装失败: {filename}")
        else:
            self.log_message(f"开始传输文件: {filename}")
            success = self.adb_manager.push_file(self.current_device, file_path)
            if success:
                self.log_message(f"文件传输成功: {filename}")
            else:
                self.log_message(f"文件传输失败: {filename}")
    
    def take_screenshot(self):
        """截图"""
        if not self.current_device or not self.adb_manager:
            return
            
        try:
            image = self.adb_manager.capture_screen(self.current_device)
            if image:
                # 保存截图
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"
                image.save(filename)
                self.log_message(f"截图已保存: {filename}")
        except Exception as e:
            self.log_message(f"截图失败: {e}")
    
    def check_scrcpy_availability(self):
        """检查scrcpy可用性"""
        if self.scrcpy_manager.is_scrcpy_available():
            self.scrcpy_status.setText("scrcpy: 可用")
            self.scrcpy_status.setStyleSheet("color: #4CAF50;")
            self.log_message("检测到scrcpy，可以使用高性能屏幕镜像")
        else:
            self.scrcpy_status.setText("scrcpy: 未安装")
            self.scrcpy_status.setStyleSheet("color: #ff6b6b;")
            self.log_message("未检测到scrcpy，将使用标准屏幕镜像")
            
            # 提示下载scrcpy
            reply = QMessageBox.question(
                self, "下载scrcpy", 
                "未检测到scrcpy，是否下载scrcpy以启用高性能屏幕镜像？\n\n"
                "scrcpy可以提供流畅的屏幕镜像体验，类似于您提到的scrcpy-win64-v3.1。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.download_scrcpy()
    
    def download_scrcpy(self):
        """下载scrcpy"""
        self.log_message("开始下载scrcpy...")
        
        # 创建进度对话框
        from PyQt5.QtWidgets import QProgressDialog
        progress = QProgressDialog("正在下载scrcpy...", "取消", 0, 0, self)
        progress.setWindowTitle("下载中")
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        progress.show()
        
        def update_status(message):
            progress.setLabelText(message)
            self.log_message(message)
        
        # 在线程中下载
        class DownloadThread(QThread):
            finished = pyqtSignal(bool)
            
            def run(self):
                success = self.parent().scrcpy_manager.download_scrcpy(update_status)
                self.finished.emit(success)
        
        download_thread = DownloadThread(self)
        download_thread.finished.connect(lambda success: self.on_scrcpy_download_finished(success, progress))
        download_thread.start()

    def on_scrcpy_download_finished(self, success, progress):
        """scrcpy下载完成"""
        progress.close()
        
        if success:
            self.log_message("scrcpy下载完成，现在可以使用高性能屏幕镜像")
            self.scrcpy_status.setText("scrcpy: 可用")
            self.scrcpy_status.setStyleSheet("color: #4CAF50;")
        else:
            self.log_message("scrcpy下载失败，将使用标准屏幕镜像")
            self.scrcpy_status.setText("scrcpy: 下载失败")
            self.scrcpy_status.setStyleSheet("color: #ff6b6b;")
            
    # ---------- Frida功能相关方法 ----------
    
    def install_frida_server(self):
        """安装Frida服务器"""
        if not self.current_device:
            QMessageBox.warning(self, "警告", "请先选择设备")
            return
            
        version = self.frida_version_combo.currentText()
        
        # 创建安装线程
        class InstallFridaThread(QThread):
            finished = pyqtSignal(bool)
            log_signal = pyqtSignal(str)
            
            def __init__(self, adb_manager, serial, version):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                self.version = version
                
            def run(self):
                try:
                    self.log_signal.emit(f"正在为设备 {self.serial} 安装Frida服务器 (版本: {self.version})...")
                    result = self.adb_manager.install_frida_server(self.serial, self.version)
                    self.finished.emit(result)
                except Exception as e:
                    self.log_signal.emit(f"安装Frida服务器时出错: {e}")
                    self.finished.emit(False)
        
        # 创建并启动线程
        self.install_frida_thread = InstallFridaThread(self.adb_manager, self.current_device, version)
        self.install_frida_thread.finished.connect(self.on_frida_install_finished)
        self.install_frida_thread.log_signal.connect(self.log_message)
        self.install_frida_thread.start()
        
        # 禁用按钮
        self.install_frida_btn.setEnabled(False)
        self.install_frida_btn.setText("安装中...")
    
    def on_frida_install_finished(self, success):
        """Frida服务器安装完成回调"""
        # 恢复按钮状态
        self.install_frida_btn.setEnabled(True)
        self.install_frida_btn.setText("安装Frida服务器")
        
        if success:
            self.log_message("Frida服务器安装成功")
            # 启用启动按钮
            self.start_frida_btn.setEnabled(True)
        else:
            self.log_message("Frida服务器安装失败")
    
    def start_frida_server(self):
        """启动Frida服务器"""
        if not self.current_device:
            QMessageBox.warning(self, "警告", "请先选择设备")
            return
            
        # 创建启动线程
        class StartFridaThread(QThread):
            finished = pyqtSignal(bool)
            log_signal = pyqtSignal(str)
            
            def __init__(self, adb_manager, serial):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                
            def run(self):
                try:
                    self.log_signal.emit(f"正在启动设备 {self.serial} 上的Frida服务器...")
                    result = self.adb_manager.start_frida_server(self.serial)
                    self.finished.emit(result)
                except Exception as e:
                    self.log_signal.emit(f"启动Frida服务器时出错: {e}")
                    self.finished.emit(False)
        
        # 创建并启动线程
        self.start_frida_thread = StartFridaThread(self.adb_manager, self.current_device)
        self.start_frida_thread.finished.connect(self.on_frida_start_finished)
        self.start_frida_thread.log_signal.connect(self.log_message)
        self.start_frida_thread.start()
        
        # 禁用按钮
        self.start_frida_btn.setEnabled(False)
        self.start_frida_btn.setText("启动中...")
    
    def on_frida_start_finished(self, success):
        """Frida服务器启动完成回调"""
        # 恢复按钮状态
        self.start_frida_btn.setEnabled(True)
        self.start_frida_btn.setText("启动Frida服务器")
        
        if success:
            self.log_message("Frida服务器启动成功")
            # 更新按钮状态
            self.start_frida_btn.setEnabled(False)
            self.stop_frida_btn.setEnabled(True)
            # 刷新应用列表
            self.refresh_app_list()
        else:
            self.log_message("Frida服务器启动失败")
    
    def stop_frida_server(self):
        """停止Frida服务器"""
        if not self.current_device:
            return
            
        # 创建停止线程
        class StopFridaThread(QThread):
            finished = pyqtSignal(bool)
            log_signal = pyqtSignal(str)
            
            def __init__(self, adb_manager, serial):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                
            def run(self):
                try:
                    self.log_signal.emit(f"正在停止设备 {self.serial} 上的Frida服务器...")
                    result = self.adb_manager.stop_frida_server(self.serial)
                    self.finished.emit(result)
                except Exception as e:
                    self.log_signal.emit(f"停止Frida服务器时出错: {e}")
                    self.finished.emit(False)
        
        # 创建并启动线程
        self.stop_frida_thread = StopFridaThread(self.adb_manager, self.current_device)
        self.stop_frida_thread.finished.connect(self.on_frida_stop_finished)
        self.stop_frida_thread.log_signal.connect(self.log_message)
        self.stop_frida_thread.start()
        
        # 禁用按钮
        self.stop_frida_btn.setEnabled(False)
        self.stop_frida_btn.setText("停止中...")
    
    def on_frida_stop_finished(self, success):
        """Frida服务器停止完成回调"""
        # 恢复按钮状态
        self.stop_frida_btn.setEnabled(False)
        self.stop_frida_btn.setText("停止Frida服务器")
        self.start_frida_btn.setEnabled(True)
        
        if success:
            self.log_message("Frida服务器已停止")
        else:
            self.log_message("无法停止Frida服务器")
    
    def check_frida_status(self):
        """检查Frida服务器状态"""
        if not self.current_device:
            QMessageBox.warning(self, "警告", "请先选择设备")
            return
            
        # 创建检查线程
        class CheckFridaThread(QThread):
            finished = pyqtSignal(bool)
            log_signal = pyqtSignal(str)
            
            def __init__(self, adb_manager, serial):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                
            def run(self):
                try:
                    self.log_signal.emit("正在检查Frida服务器状态...")
                    # 检查frida-server进程是否存在
                    result = self.adb_manager.execute_command(self.serial, "ps -A | grep frida-server")
                    if "frida-server" in result:
                        self.log_signal.emit("Frida服务器正在运行")
                        self.finished.emit(True)
                    else:
                        self.log_signal.emit("Frida服务器未运行")
                        self.finished.emit(False)
                except Exception as e:
                    self.log_signal.emit(f"检查Frida状态时出错: {e}")
                    self.finished.emit(False)
        
        # 创建并启动线程
        self.check_frida_thread = CheckFridaThread(self.adb_manager, self.current_device)
        self.check_frida_thread.finished.connect(self.on_frida_check_finished)
        self.check_frida_thread.log_signal.connect(self.log_message)
        self.check_frida_thread.start()
    
    def on_frida_check_finished(self, is_running):
        """Frida状态检查完成回调"""
        # 更新按钮状态
        self.start_frida_btn.setEnabled(not is_running)
        self.stop_frida_btn.setEnabled(is_running)
        
        # 如果服务器正在运行，刷新应用列表
        if is_running:
            self.refresh_app_list()
    
    def refresh_app_list(self):
        """刷新应用列表"""
        if not self.current_device:
            QMessageBox.warning(self, "警告", "请先选择设备")
            return
            
        # 创建刷新线程
        class RefreshAppsThread(QThread):
            finished = pyqtSignal(list)
            log_signal = pyqtSignal(str)
            
            def __init__(self, adb_manager, serial):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                
            def run(self):
                try:
                    self.log_signal.emit(f"正在获取设备 {self.serial} 上运行的应用...")
                    apps = self.adb_manager.list_running_apps(self.serial)
                    self.finished.emit(apps)
                except Exception as e:
                    self.log_signal.emit(f"获取应用列表时出错: {e}")
                    self.finished.emit([])
        
        # 创建并启动线程
        self.refresh_apps_thread = RefreshAppsThread(self.adb_manager, self.current_device)
        self.refresh_apps_thread.finished.connect(self.on_app_list_refreshed)
        self.refresh_apps_thread.log_signal.connect(self.log_message)
        self.refresh_apps_thread.start()
        
        # 禁用按钮
        self.refresh_apps_btn.setEnabled(False)
        self.refresh_apps_btn.setText("刷新中...")
    
    def on_app_list_refreshed(self, apps):
        """应用列表刷新完成回调"""
        # 恢复按钮状态
        self.refresh_apps_btn.setEnabled(True)
        self.refresh_apps_btn.setText("刷新应用列表")
        
        # 更新应用列表
        self.app_list_combo.clear()
        if apps:
            for app in apps:
                self.app_list_combo.addItem(f"{app['package_name']} (PID: {app['pid']})")
            self.log_message(f"获取到 {len(apps)} 个运行中的应用")
        else:
            self.log_message("未获取到运行中的应用")
    
    def on_app_selected(self, text):
        """应用选择事件"""
        if text:
            # 提取包名
            package_name = text.split(" (PID:")[0].strip()
            self.frida_package_input.setText(package_name)
    
    def select_frida_script(self):
        """选择Frida脚本文件"""
        from PyQt5.QtWidgets import QFileDialog, QMenu, QAction
        
        # 创建上下文菜单
        menu = QMenu(self)
        open_action = QAction("打开现有脚本", self)
        create_action = QAction("创建示例脚本", self)
        
        menu.addAction(open_action)
        menu.addAction(create_action)
        
        # 显示菜单
        action = menu.exec_(QCursor.pos())
        
        if action == open_action:
            # 打开现有脚本
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择Frida脚本文件", "", "JavaScript文件 (*.js);;所有文件 (*.*)"
            )
            
            if file_path:
                self.frida_script_path.setText(file_path)
        elif action == create_action:
            # 创建示例脚本
            self.create_sample_script()
    
    def create_sample_script(self):
        """创建示例Frida脚本"""
        from PyQt5.QtWidgets import QFileDialog
        
        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存示例脚本", "frida_sample_script.js", "JavaScript文件 (*.js)"
        )
        
        if not file_path:
            return
            
        # 示例脚本内容
        sample_script = """// Frida示例脚本 - 基本功能演示
// 此脚本展示了如何Hook常见Android API

Java.perform(function() {
    console.log("[*] Frida示例脚本已加载");
    
    // ======== 1. Hook加密API ========
    try {
        var Cipher = Java.use("javax.crypto.Cipher");
        
        // Hook初始化方法
        Cipher.init.overload('int', 'java.security.Key', 'java.security.spec.AlgorithmParameterSpec').implementation = function(opmode, key, spec) {
            console.log("[+] Cipher.init 被调用");
            console.log("    模式: " + opmode + " (1=加密, 2=解密)");
            
            // 调用原始方法
            var result = this.init(opmode, key, spec);
            
            // 获取算法信息
            console.log("    算法: " + this.getAlgorithm());
            
            return result;
        };
        
        // Hook加密/解密方法
        Cipher.doFinal.overload('[B').implementation = function(input) {
            console.log("[+] Cipher.doFinal 被调用");
            
            // 打印输入数据
            console.log("    输入数据: " + bytesToHex(input));
            
            // 调用原始方法
            var result = this.doFinal(input);
            
            // 打印输出数据
            console.log("    输出数据: " + bytesToHex(result));
            
            return result;
        };
        
        console.log("[*] 已Hook加密API");
    } catch(e) {
        console.log("[-] Hook加密API失败: " + e);
    }
    
    // ======== 2. Hook网络请求 ========
    try {
        // Hook OkHttp (常用的HTTP客户端)
        try {
            var OkHttpClient = Java.use("okhttp3.OkHttpClient");
            var Request = Java.use("okhttp3.Request");
            var Response = Java.use("okhttp3.Response");
            
            // Hook newCall方法
            OkHttpClient.newCall.implementation = function(request) {
                console.log("[+] OkHttp发起请求");
                console.log("    URL: " + request.url().toString());
                console.log("    方法: " + request.method());
                
                // 获取请求头
                var headers = request.headers();
                var headersSize = headers.size();
                if(headersSize > 0) {
                    console.log("    请求头:");
                    for(var i = 0; i < headersSize; i++) {
                        var name = headers.name(i);
                        var value = headers.value(i);
                        console.log("      " + name + ": " + value);
                    }
                }
                
                // 调用原始方法
                var call = this.newCall(request);
                
                return call;
            };
            
            console.log("[*] 已Hook OkHttp");
        } catch(e) {
            console.log("[-] Hook OkHttp失败: " + e);
        }
    } catch(e) {
        console.log("[-] Hook网络请求失败: " + e);
    }
    
    // ======== 3. Hook文件操作 ========
    try {
        var FileOutputStream = Java.use("java.io.FileOutputStream");
        
        // Hook写入方法
        FileOutputStream.write.overload('[B', 'int', 'int').implementation = function(buffer, offset, count) {
            console.log("[+] 文件写入操作");
            console.log("    文件路径: " + this.toString());
            console.log("    写入数据: " + bytesToHex(buffer));
            
            // 调用原始方法
            this.write(buffer, offset, count);
        };
        
        console.log("[*] 已Hook文件操作");
    } catch(e) {
        console.log("[-] Hook文件操作失败: " + e);
    }
    
    // ======== 4. Hook SharedPreferences ========
    try {
        var SharedPreferencesImpl = Java.use("android.app.SharedPreferencesImpl$EditorImpl");
        
        // Hook putString方法
        SharedPreferencesImpl.putString.implementation = function(key, value) {
            console.log("[+] SharedPreferences.putString");
            console.log("    键: " + key);
            console.log("    值: " + value);
            
            // 调用原始方法
            return this.putString(key, value);
        };
        
        console.log("[*] 已Hook SharedPreferences");
    } catch(e) {
        console.log("[-] Hook SharedPreferences失败: " + e);
    }
    
    // ======== 辅助函数 ========
    function bytesToHex(bytes) {
        if(!bytes || bytes.length === 0) return "<空>";
        
        // 只显示前50个字节，避免输出过长
        var len = bytes.length;
        var maxDisplay = 50;
        var toDisplay = len > maxDisplay ? maxDisplay : len;
        
        var hex = "";
        for(var i = 0; i < toDisplay; i++) {
            var b = bytes[i] & 0xFF;
            if(b < 0x10) hex += "0";
            hex += b.toString(16);
            if(i < toDisplay - 1) hex += " ";
        }
        
        if(len > maxDisplay) {
            hex += "... (" + len + " 字节)";
        }
        
        return hex;
    }
});

console.log("[*] 示例脚本已准备就绪，等待应用启动...");
"""
        
        try:
            # 使用UTF-8编码保存文件
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(sample_script)
                
            self.frida_script_path.setText(file_path)
            self.log_message(f"示例脚本已创建: {file_path}")
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"创建脚本文件失败: {str(e)}")
    
    # 示例脚本方法已移除
    
    def run_frida_script(self):
        """执行Frida脚本"""
        if not self.current_device:
            QMessageBox.warning(self, "警告", "请先选择设备")
            return
            
        # 检查包名
        package_name = self.frida_package_input.text().strip()
        if not package_name:
            QMessageBox.warning(self, "警告", "请输入目标应用包名")
            return
            
        # 检查脚本路径
        script_path = self.frida_script_path.text()
        if not script_path:
            QMessageBox.warning(self, "警告", "请选择Frida脚本")
            return
            
        if not os.path.exists(script_path):
            QMessageBox.warning(self, "警告", f"脚本文件不存在: {script_path}")
            return
            
        # 创建执行线程
        class RunScriptThread(QThread):
            finished = pyqtSignal(bool)
            log_signal = pyqtSignal(str)
            
            def __init__(self, adb_manager, serial, script_path, package_name):
                super().__init__()
                self.adb_manager = adb_manager
                self.serial = serial
                self.script_path = script_path
                self.package_name = package_name
                
            def run(self):
                try:
                    self.log_signal.emit(f"正在对设备 {self.serial} 上的应用 {self.package_name} 执行Hook脚本...")
                    
                    # 检查frida服务器是否在运行
                    result = self.adb_manager.execute_command(self.serial, "ps -A | grep frida-server")
                    if "frida-server" not in result:
                        self.log_signal.emit("Frida服务器未运行，正在启动...")
                        if not self.adb_manager.start_frida_server(self.serial):
                            self.log_signal.emit("无法启动Frida服务器，请先启动Frida服务器")
                            self.finished.emit(False)
                            return
                        # 等待服务器启动
                        import time
                        time.sleep(2)
                    
                    # 检查frida模块是否已安装
                    import importlib.util
                    if importlib.util.find_spec("frida") is None:
                        self.log_signal.emit("正在安装frida模块...")
                        import subprocess, sys
                        try:
                            subprocess.check_call([sys.executable, "-m", "pip", "install", "frida", "frida-tools"])
                            self.log_signal.emit("frida模块安装成功")
                        except Exception as e:
                            self.log_signal.emit(f"安装frida模块失败: {str(e)}")
                            self.finished.emit(False)
                            return
                    
                    # 尝试执行脚本
                    self.log_signal.emit(f"正在执行脚本: {os.path.basename(self.script_path)}")
                    result = self.adb_manager.execute_frida_script(self.serial, self.script_path, self.package_name)
                    self.finished.emit(result)
                except Exception as e:
                    self.log_signal.emit(f"执行Frida脚本时出错: {str(e)}")
                    import traceback
                    self.log_signal.emit(traceback.format_exc())
                    self.finished.emit(False)
        
        # 创建并启动线程
        self.run_script_thread = RunScriptThread(
            self.adb_manager, self.current_device, script_path, package_name
        )
        self.run_script_thread.finished.connect(self.on_script_execution_finished)
        self.run_script_thread.log_signal.connect(self.log_message)
        self.run_script_thread.start()
        
        # 更新按钮状态
        self.run_script_btn.setEnabled(False)
        self.stop_script_btn.setEnabled(True)
    
    def on_script_execution_finished(self, success):
        """脚本执行完成回调"""
        if success:
            self.log_message("Frida脚本已启动")
        else:
            # 恢复按钮状态
            self.run_script_btn.setEnabled(True)
            self.stop_script_btn.setEnabled(False)
            self.log_message("执行Frida脚本失败")
    
    def stop_frida_script(self):
        """停止Frida脚本"""
        # 恢复按钮状态
        self.run_script_btn.setEnabled(True)
        self.stop_script_btn.setEnabled(False)
        
        # 目前没有直接方法停止脚本，只能通知用户
        self.log_message("请在Frida脚本控制台按Ctrl+C停止脚本")