{"permissions": {"allow": ["Bash(dir \"C:\\Users\\<USER>\\Desktop\\Android\\jedx-mcp\\weixin8061android2880_0x28003d34_arm64.apk.jadx\" /A)", "Bash(ls:*)", "Bash(claude mcp add-json jadx_mcp_server '{\"\"command\"\": \"\"uv\"\", \"\"args\"\": [\"\"--directory\"\", \"\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Android\\\\jedx-mcp\\\\jadx-mcp-server\"\", \"\"run\"\", \"\"jadx_mcp_server.py\"\"]}')", "Bash(where git)", "Bash(set CLAUDE_CODE_GIT_BASH_PATH=C:Program FilesGitbinbash.exe)", "Bash(claude mcp add-json jadx_mcp_server \"{\"\"command\"\": \"\"uv\"\", \"\"args\"\": [\"\"--directory\"\", \"\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Android\\\\jedx-mcp\\\\jadx-mcp-server\"\", \"\"run\"\", \"\"jadx_mcp_server.py\"\"]}\")", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(uv:*)", "<PERSON><PERSON>(compgen:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(curl:*)"], "deny": []}}