/**
 * 微信消息发送简化Hook脚本 - 动态版本
 * 自动查找正确的类名，适配不同版本的微信
 * 专注于核心消息发送流程的监控
 */

console.log("🚀 微信消息发送动态Hook脚本启动");

Java.perform(function() {
    console.log("[+] Java环境初始化完成");

    // 消息计数器
    var messageCount = 0;

    // 工具函数：查找包含特定方法的类
    function findClassWithMethods(packagePrefix, methodNames) {
        var foundClasses = [];
        Java.enumerateLoadedClasses({
            onMatch: function(className) {
                if (className.startsWith(packagePrefix)) {
                    try {
                        var clazz = Java.use(className);
                        var hasAllMethods = methodNames.every(function(methodName) {
                            try {
                                return clazz[methodName] !== undefined;
                            } catch (e) {
                                return false;
                            }
                        });
                        if (hasAllMethods) {
                            foundClasses.push(className);
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }
            },
            onComplete: function() {}
        });
        return foundClasses;
    }

    // 查找消息类
    console.log("[*] 正在查找消息相关类...");
    var messageClasses = findClassWithMethods("", ["setType", "getContent"]);
    console.log(`[+] 找到 ${messageClasses.length} 个可能的消息类`);

    var MessageClass = null;
    for (var i = 0; i < messageClasses.length; i++) {
        try {
            var testClass = Java.use(messageClasses[i]);
            // 检查是否有我们需要的方法
            if (testClass.setType && testClass.getContent) {
                MessageClass = testClass;
                console.log(`[+] 找到消息类: ${messageClasses[i]}`);
                break;
            }
        } catch (e) {
            continue;
        }
    }

    if (MessageClass) {
        
        // Hook 设置消息内容
        MessageClass.m94407d1.implementation = function(content) {
            messageCount++;
            console.log("\n" + "=".repeat(50));
            console.log(`📝 [消息 #${messageCount}] 内容设置`);
            console.log(`内容: ${content}`);
            console.log(`长度: ${content ? content.length : 0} 字符`);
            console.log(`时间: ${new Date().toLocaleString('zh-CN')}`);
            
            return this.m94407d1(content);
        };
        
        // Hook 设置接收人
        MessageClass.m94391J1.implementation = function(talker) {
            console.log(`👤 接收人: ${talker}`);
            return this.m94391J1(talker);
        };
        
        // Hook 设置消息类型
        MessageClass.setType.implementation = function(type) {
            var typeDesc = {
                1: "文本", 3: "图片", 34: "语音", 
                43: "视频", 47: "表情", 49: "链接"
            }[type] || `未知(${type})`;
            
            console.log(`📋 消息类型: ${typeDesc}`);
            return this.setType(type);
        };
        
        console.log("[✅] 消息对象Hook设置完成");
        
    } catch (e) {
        console.log(`[❌] Hook消息对象失败: ${e}`);
    }
    
    try {
        // Hook 核心发送方法
        var SenderClass = Java.use("et0.C77023o");
        console.log("[+] 找到发送类: et0.C77023o");
        
        SenderClass.mo62223i.implementation = function(container, config, continuation) {
            console.log("\n🚀 [核心发送] 消息即将发送");
            console.log(`容器: ${container}`);
            console.log(`配置: ${config}`);
            
            // 记录发送时间
            var sendTime = new Date().toLocaleString('zh-CN');
            console.log(`发送时间: ${sendTime}`);
            
            var result = this.mo62223i(container, config, continuation);
            console.log("✅ 消息发送完成");
            console.log("=".repeat(50));
            
            return result;
        };
        
        console.log("[✅] 核心发送Hook设置完成");
        
    } catch (e) {
        console.log(`[❌] Hook核心发送失败: ${e}`);
    }
    
    try {
        // Hook UI发送按钮
        var ChatFooterClass = Java.use("com.tencent.mm.pluginsdk.ui.chat.ChatFooter");
        console.log("[+] 找到ChatFooter类");
        
        ChatFooterClass.A.implementation = function() {
            console.log("\n🎯 [UI事件] 发送按钮被点击");
            var result = this.A.apply(this, arguments);
            return result;
        };
        
        console.log("[✅] UI交互Hook设置完成");
        
    } catch (e) {
        console.log(`[❌] Hook UI交互失败: ${e}`);
    }
    
    // 定期统计
    setInterval(function() {
        if (messageCount > 0) {
            console.log(`\n📊 统计: 已发送 ${messageCount} 条消息`);
        }
    }, 60000); // 每分钟统计一次
    
    console.log("\n" + "=".repeat(60));
    console.log("🎉 简化Hook脚本初始化完成！");
    console.log("📱 现在可以在微信中发送消息来测试Hook效果");
    console.log("=".repeat(60));
});
