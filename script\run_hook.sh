#!/bin/bash

# 微信消息发送Hook脚本启动器 (Linux/Mac版本)

echo "============================================================"
echo "微信消息发送Hook脚本启动器"
echo "============================================================"
echo

show_menu() {
    echo "请选择要运行的Hook脚本:"
    echo
    echo "[1] 完整版Hook脚本 (hook_msg.js)"
    echo "    - 监控完整的消息发送流程"
    echo "    - 包含调用堆栈、网络请求、数据库操作等"
    echo "    - 适合深度分析"
    echo
    echo "[2] 简化版Hook脚本 (hook_msg_simple.js)"
    echo "    - 只监控核心消息发送过程"
    echo "    - 输出简洁，性能影响小"
    echo "    - 适合快速测试"
    echo
    echo "[3] 检查Frida和设备连接状态"
    echo
    echo "[4] 退出"
    echo
}

check_frida() {
    if ! command -v frida &> /dev/null; then
        echo "❌ Frida未安装或不在PATH中"
        echo "请先安装Frida: pip install frida-tools"
        return 1
    fi
    return 0
}

full_hook() {
    echo
    echo "启动完整版Hook脚本..."
    echo "============================================================"
    echo "注意: 完整版脚本会产生大量日志输出"
    echo "建议将输出重定向到文件:"
    echo "frida -U com.tencent.mm -l hook_msg.js > output.log"
    echo "============================================================"
    echo
    read -p "按Enter键继续..."
    
    if [ -f "hook_msg.js" ]; then
        frida -U com.tencent.mm -l hook_msg.js
    else
        echo "❌ 找不到 hook_msg.js 文件"
        echo "请确保在正确的目录下运行此脚本"
    fi
}

simple_hook() {
    echo
    echo "启动简化版Hook脚本..."
    echo "============================================================"
    echo "简化版脚本只显示关键信息，适合实时监控"
    echo "============================================================"
    echo
    read -p "按Enter键继续..."
    
    if [ -f "hook_msg_simple.js" ]; then
        frida -U com.tencent.mm -l hook_msg_simple.js
    else
        echo "❌ 找不到 hook_msg_simple.js 文件"
        echo "请确保在正确的目录下运行此脚本"
    fi
}

check_status() {
    echo
    echo "检查Frida和设备状态..."
    echo "============================================================"
    echo
    
    echo "1. 检查Frida版本:"
    frida --version
    echo
    
    echo "2. 检查连接的设备:"
    frida-ls-devices
    echo
    
    echo "3. 检查微信进程:"
    frida-ps -U | grep tencent || echo "未找到微信进程"
    echo
    
    echo "============================================================"
    read -p "按Enter键返回主菜单..."
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            if check_frida; then
                full_hook
            fi
            ;;
        2)
            if check_frida; then
                simple_hook
            fi
            ;;
        3)
            if check_frida; then
                check_status
            fi
            ;;
        4)
            echo
            echo "感谢使用微信消息Hook脚本！"
            echo
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
    
    echo
done
