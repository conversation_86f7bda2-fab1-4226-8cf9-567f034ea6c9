{"script_info": {"name": "微信消息发送Hook脚本", "version": "1.0.0", "author": "AI Assistant", "description": "基于JADX反编译代码分析的微信消息发送流程Hook脚本"}, "target_app": {"package_name": "com.tencent.mm", "app_name": "微信", "supported_versions": ["8.0.x", "8.1.x", "8.2.x"]}, "hook_classes": {"message_class": {"name": "p713qk.C127133t7", "description": "消息对象类", "methods": {"set_content": "m94407d1", "set_talker": "m94391J1", "set_create_time": "m94408e1", "set_type": "setType", "set_is_send": "m94412n1", "get_content": "get<PERSON>ontent", "get_create_time": "getCreateTime"}}, "sender_class": {"name": "et0.C77023o", "description": "消息发送核心类", "methods": {"core_send": "mo62223i"}}, "ui_class": {"name": "com.tencent.mm.pluginsdk.ui.chat.ChatFooter", "description": "聊天界面底部栏", "methods": {"send_button_click": "A"}}, "edittext_class": {"name": "com.tencent.mm.ui.widget.MMEditText", "description": "微信输入框", "methods": {"get_text": "getText"}}}, "message_types": {"1": "文本消息", "3": "图片消息", "34": "语音消息", "43": "视频消息", "47": "表情消息", "48": "位置消息", "49": "链接消息", "10000": "系统消息"}, "hook_options": {"enable_stack_trace": true, "enable_network_monitor": true, "enable_database_monitor": true, "enable_ui_monitor": true, "enable_statistics": true, "statistics_interval": 30000, "log_level": "info"}, "output_settings": {"console_output": true, "file_output": false, "output_file": "wechat_hook.log", "max_content_length": 200, "timestamp_format": "zh-CN"}, "filter_settings": {"enable_content_filter": false, "content_keywords": [], "enable_user_filter": false, "target_users": [], "enable_type_filter": false, "target_types": [1, 3, 34, 43]}}